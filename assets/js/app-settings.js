// Application Settings JavaScript
(function() {
    'use strict';

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        initializeAppSettings();
    });

    // Initialize application settings functionality
    function initializeAppSettings() {
        setupEventListeners();
        loadSettings();
    }

    // Setup event listeners
    function setupEventListeners() {
        // Form submissions
        const forms = [
            'generalSettingsForm',
            'appearanceSettingsForm',
            'emailSettingsForm',
            'smsSettingsForm',
            'paymentSettingsForm',
            'securitySettingsForm'
        ];

        forms.forEach(formId => {
            const form = document.getElementById(formId);
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    saveFormSettings(formId);
                });
            }
        });

        // Theme selection
        const themeCards = document.querySelectorAll('.theme-card');
        themeCards.forEach(card => {
            card.addEventListener('click', function() {
                const theme = this.dataset.theme;
                selectTheme(theme);
            });
        });

        // Primary color change
        const primaryColorInput = document.getElementById('primaryColor');
        if (primaryColorInput) {
            primaryColorInput.addEventListener('change', function() {
                updatePrimaryColor(this.value);
            });
        }

        // File uploads
        const fileInputs = ['logoUpload', 'faviconUpload'];
        fileInputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input) {
                input.addEventListener('change', function() {
                    handleFileUpload(this);
                });
            }
        });

        // Integration toggles
        const integrationToggles = [
            'googleAnalytics',
            'slackIntegration',
            'mailchimpIntegration',
            'zapierIntegration'
        ];

        integrationToggles.forEach(toggleId => {
            const toggle = document.getElementById(toggleId);
            if (toggle) {
                toggle.addEventListener('change', function() {
                    toggleIntegration(toggleId, this.checked);
                });
            }
        });
    }

    // Load current settings
    function loadSettings() {
        // In a real application, this would load settings from an API
        // For now, we'll use default values that are already in the HTML
        console.log('Settings loaded');
    }

    // Save form settings
    function saveFormSettings(formId) {
        const form = document.getElementById(formId);
        if (!form) return;

        const formData = new FormData(form);
        const settings = {};

        // Convert FormData to object
        for (let [key, value] of formData.entries()) {
            settings[key] = value;
        }

        // Include checkbox values that might not be in FormData
        const checkboxes = form.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            settings[checkbox.id] = checkbox.checked;
        });

        // Simulate API call
        setTimeout(() => {
            Dashboard.showToast('Settings saved successfully!', 'success');
            console.log('Saved settings:', settings);
        }, 500);
    }

    // Select theme
    function selectTheme(theme) {
        const themeRadios = document.querySelectorAll('input[name="theme"]');
        themeRadios.forEach(radio => {
            radio.checked = radio.value === theme;
        });

        // Update theme cards visual state
        const themeCards = document.querySelectorAll('.theme-card');
        themeCards.forEach(card => {
            card.classList.toggle('border-primary', card.dataset.theme === theme);
        });

        Dashboard.showToast(`${theme.charAt(0).toUpperCase() + theme.slice(1)} theme selected`, 'info');
    }

    // Update primary color
    function updatePrimaryColor(color) {
        const colorDisplay = document.querySelector('#primaryColor + .text-muted');
        if (colorDisplay) {
            colorDisplay.textContent = color;
        }

        // In a real application, this would update CSS custom properties
        document.documentElement.style.setProperty('--bs-primary', color);
        Dashboard.showToast('Primary color updated', 'info');
    }

    // Handle file uploads
    function handleFileUpload(input) {
        const file = input.files[0];
        if (!file) return;

        // Validate file size (2MB limit)
        if (file.size > 2 * 1024 * 1024) {
            Dashboard.showToast('File size must be less than 2MB', 'error');
            input.value = '';
            return;
        }

        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/png', 'image/svg+xml', 'image/x-icon'];
        if (!allowedTypes.includes(file.type)) {
            Dashboard.showToast('Please select a valid image file', 'error');
            input.value = '';
            return;
        }

        // Simulate upload
        Dashboard.showToast(`${file.name} uploaded successfully!`, 'success');
    }

    // Toggle integration
    function toggleIntegration(integrationId, enabled) {
        const integrationName = integrationId.replace('Integration', '').replace(/([A-Z])/g, ' $1').trim();
        const status = enabled ? 'enabled' : 'disabled';
        Dashboard.showToast(`${integrationName} ${status}`, 'info');
    }

    // Test email settings
    function testEmailSettings() {
        const smtpHost = document.getElementById('smtpHost').value;
        const smtpPort = document.getElementById('smtpPort').value;
        const fromEmail = document.getElementById('fromEmail').value;

        if (!smtpHost || !smtpPort || !fromEmail) {
            Dashboard.showToast('Please fill in all required email settings', 'error');
            return;
        }

        // Simulate email test
        Dashboard.showToast('Sending test email...', 'info');
        setTimeout(() => {
            Dashboard.showToast('Test email sent successfully!', 'success');
        }, 2000);
    }

    // Test SMS settings
    function testSmsSettings() {
        const smsProvider = document.getElementById('smsProvider').value;
        const smsApiKey = document.getElementById('smsApiKey').value;
        const smsFromNumber = document.getElementById('smsFromNumber').value;

        if (!smsProvider || !smsApiKey || !smsFromNumber) {
            Dashboard.showToast('Please fill in all required SMS settings', 'error');
            return;
        }

        // Simulate SMS test
        Dashboard.showToast('Sending test SMS...', 'info');
        setTimeout(() => {
            Dashboard.showToast('Test SMS sent successfully!', 'success');
        }, 2000);
    }

    // Save integrations
    function saveIntegrations() {
        const integrations = {
            googleAnalytics: {
                enabled: document.getElementById('googleAnalytics').checked,
                trackingId: document.getElementById('gaTrackingId').value
            },
            slack: {
                enabled: document.getElementById('slackIntegration').checked,
                webhookUrl: document.getElementById('slackWebhook').value
            },
            mailchimp: {
                enabled: document.getElementById('mailchimpIntegration').checked,
                apiKey: document.getElementById('mailchimpApiKey').value
            },
            zapier: {
                enabled: document.getElementById('zapierIntegration').checked,
                apiKey: document.getElementById('zapierApiKey').value
            }
        };

        // Simulate API call
        setTimeout(() => {
            Dashboard.showToast('Integration settings saved successfully!', 'success');
            console.log('Saved integrations:', integrations);
        }, 500);
    }

    // Create backup
    function createBackup() {
        Dashboard.showToast('Creating backup...', 'info');
        
        // Simulate backup creation
        setTimeout(() => {
            Dashboard.showToast('Backup created successfully!', 'success');
            
            // Add new backup to the table (in a real app, this would refresh from API)
            const tbody = document.querySelector('#backup table tbody');
            if (tbody) {
                const newRow = document.createElement('tr');
                const now = new Date();
                const dateStr = now.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                }) + ' ' + now.toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true
                });
                
                newRow.innerHTML = `
                    <td>${dateStr}</td>
                    <td>247 MB</td>
                    <td><span class="badge bg-success">Completed</span></td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary">Download</button>
                        <button class="btn btn-sm btn-outline-secondary">Restore</button>
                    </td>
                `;
                
                tbody.insertBefore(newRow, tbody.firstChild);
            }
        }, 3000);
    }

    // Save backup settings
    function saveBackupSettings() {
        const backupSettings = {
            autoBackup: document.getElementById('autoBackup').checked,
            frequency: document.getElementById('backupFrequency').value,
            retention: document.getElementById('backupRetention').value
        };

        // Simulate API call
        setTimeout(() => {
            Dashboard.showToast('Backup settings saved successfully!', 'success');
            console.log('Saved backup settings:', backupSettings);
        }, 500);
    }

    // Utility function to show confirmation dialog
    function showConfirmDialog(message, callback) {
        if (confirm(message)) {
            callback();
        }
    }

    // Export functions to global scope
    window.testEmailSettings = testEmailSettings;
    window.testSmsSettings = testSmsSettings;
    window.saveIntegrations = saveIntegrations;
    window.createBackup = createBackup;
    window.saveBackupSettings = saveBackupSettings;

})();
