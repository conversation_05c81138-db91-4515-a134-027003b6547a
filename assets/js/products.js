// Products Management JavaScript
(function() {
    'use strict';

    // Sample products data (in a real app, this would come from an API)
    let products = [
        {
            id: 1,
            name: 'Starter Plan',
            description: 'Perfect for small teams',
            category: 'subscription',
            price: 29.00,
            stock: null, // unlimited
            status: 'active',
            featured: true,
            image: '../assets/images/products/product-1.jpg',
            created: '2024-12-01'
        },
        {
            id: 2,
            name: 'Professional Plan',
            description: 'Best for growing businesses',
            category: 'subscription',
            price: 79.00,
            stock: null,
            status: 'active',
            featured: true,
            image: '../assets/images/products/product-2.jpg',
            created: '2024-12-01'
        },
        {
            id: 3,
            name: 'Enterprise Plan',
            description: 'For large organizations',
            category: 'subscription',
            price: 199.00,
            stock: null,
            status: 'active',
            featured: false,
            image: '../assets/images/products/product-3.jpg',
            created: '2024-12-01'
        },
        {
            id: 4,
            name: 'API Access',
            description: 'Developer API integration',
            category: 'service',
            price: 49.00,
            stock: null,
            status: 'draft',
            featured: false,
            image: '../assets/images/products/product-4.jpg',
            created: '2024-11-28'
        },
        {
            id: 5,
            name: 'Custom Integration',
            description: 'One-time setup service',
            category: 'service',
            price: 299.00,
            stock: 50,
            status: 'inactive',
            featured: false,
            image: '../assets/images/products/product-5.jpg',
            created: '2024-11-25'
        }
    ];

    let currentEditingId = null;
    let filteredProducts = [...products];

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        initializeProducts();
    });

    // Initialize products functionality
    function initializeProducts() {
        setupEventListeners();
        renderProducts();
        updateProductCount();
    }

    // Setup event listeners
    function setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('productSearch');
        if (searchInput) {
            searchInput.addEventListener('input', debounce(handleSearch, 300));
        }

        // Filter functionality
        const categoryFilter = document.getElementById('categoryFilter');
        const statusFilter = document.getElementById('statusFilter');
        const sortBy = document.getElementById('sortBy');

        if (categoryFilter) categoryFilter.addEventListener('change', applyFilters);
        if (statusFilter) statusFilter.addEventListener('change', applyFilters);
        if (sortBy) sortBy.addEventListener('change', applyFilters);

        // Select all checkbox
        const selectAll = document.getElementById('selectAll');
        if (selectAll) {
            selectAll.addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('.product-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateBulkActions();
            });
        }

        // Product form submission
        const productForm = document.getElementById('productForm');
        if (productForm) {
            productForm.addEventListener('submit', function(e) {
                e.preventDefault();
                saveProduct();
            });
        }
    }

    // Handle search
    function handleSearch(e) {
        const query = e.target.value.toLowerCase();
        filteredProducts = products.filter(product => 
            product.name.toLowerCase().includes(query) ||
            product.description.toLowerCase().includes(query) ||
            product.category.toLowerCase().includes(query)
        );
        applyFilters();
    }

    // Apply filters and sorting
    function applyFilters() {
        const categoryFilter = document.getElementById('categoryFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;
        const sortBy = document.getElementById('sortBy').value;

        // Apply filters
        filteredProducts = products.filter(product => {
            const categoryMatch = !categoryFilter || product.category === categoryFilter;
            const statusMatch = !statusFilter || product.status === statusFilter;
            return categoryMatch && statusMatch;
        });

        // Apply search if there's a search query
        const searchQuery = document.getElementById('productSearch')?.value.toLowerCase();
        if (searchQuery) {
            filteredProducts = filteredProducts.filter(product => 
                product.name.toLowerCase().includes(searchQuery) ||
                product.description.toLowerCase().includes(searchQuery) ||
                product.category.toLowerCase().includes(searchQuery)
            );
        }

        // Apply sorting
        filteredProducts.sort((a, b) => {
            switch (sortBy) {
                case 'name':
                    return a.name.localeCompare(b.name);
                case 'price':
                    return b.price - a.price;
                case 'created':
                    return new Date(b.created) - new Date(a.created);
                default:
                    return 0;
            }
        });

        renderProducts();
        updateProductCount();
    }

    // Render products table
    function renderProducts() {
        const tbody = document.getElementById('productsTableBody');
        if (!tbody) return;

        if (filteredProducts.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <div class="text-muted">
                            <span class="material-icons-outlined mb-2" style="font-size: 48px;">inventory_2</span>
                            <p>No products found</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = filteredProducts.map(product => `
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input product-checkbox" data-id="${product.id}">
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <img src="${product.image}" alt="Product" class="rounded me-3" width="40" height="40">
                        <div>
                            <h6 class="mb-0">${product.name}</h6>
                            <small class="text-muted">${product.description}</small>
                        </div>
                    </div>
                </td>
                <td><span class="badge bg-${getCategoryColor(product.category)}">${formatCategory(product.category)}</span></td>
                <td>${formatPrice(product.price)}</td>
                <td><span class="badge bg-${getStatusColor(product.status)}">${formatStatus(product.status)}</span></td>
                <td>${product.stock === null ? 'Unlimited' : product.stock}</td>
                <td>${formatDate(product.created)}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editProduct(${product.id})" title="Edit">
                            <span class="material-icons-outlined">edit</span>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="viewProduct(${product.id})" title="View">
                            <span class="material-icons-outlined">visibility</span>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteProduct(${product.id})" title="Delete">
                            <span class="material-icons-outlined">delete</span>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        // Re-attach checkbox event listeners
        const checkboxes = document.querySelectorAll('.product-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateBulkActions);
        });
    }

    // Open product modal for adding/editing
    function openProductModal(productId = null) {
        currentEditingId = productId;
        const modal = document.getElementById('productModal');
        const modalTitle = document.getElementById('productModalTitle');
        const form = document.getElementById('productForm');

        if (productId) {
            // Edit mode
            const product = products.find(p => p.id === productId);
            if (product) {
                modalTitle.textContent = 'Edit Product';
                populateForm(product);
            }
        } else {
            // Add mode
            modalTitle.textContent = 'Add New Product';
            form.reset();
            currentEditingId = null;
        }
    }

    // Populate form with product data
    function populateForm(product) {
        document.getElementById('productId').value = product.id;
        document.getElementById('productName').value = product.name;
        document.getElementById('productDescription').value = product.description;
        document.getElementById('productCategory').value = product.category;
        document.getElementById('productPrice').value = product.price;
        document.getElementById('productStock').value = product.stock || '';
        document.getElementById('productStatus').value = product.status;
        document.getElementById('productFeatured').checked = product.featured;
    }

    // Save product (add or update)
    function saveProduct() {
        const form = document.getElementById('productForm');
        if (!form.checkValidity()) {
            form.classList.add('was-validated');
            return;
        }

        const formData = {
            name: document.getElementById('productName').value,
            description: document.getElementById('productDescription').value,
            category: document.getElementById('productCategory').value,
            price: parseFloat(document.getElementById('productPrice').value),
            stock: document.getElementById('productStock').value ? parseInt(document.getElementById('productStock').value) : null,
            status: document.getElementById('productStatus').value,
            featured: document.getElementById('productFeatured').checked
        };

        if (currentEditingId) {
            // Update existing product
            const index = products.findIndex(p => p.id === currentEditingId);
            if (index !== -1) {
                products[index] = { ...products[index], ...formData };
                Dashboard.showToast('Product updated successfully!', 'success');
            }
        } else {
            // Add new product
            const newProduct = {
                id: Math.max(...products.map(p => p.id)) + 1,
                ...formData,
                image: '../assets/images/products/product-default.jpg',
                created: new Date().toISOString().split('T')[0]
            };
            products.push(newProduct);
            Dashboard.showToast('Product added successfully!', 'success');
        }

        // Close modal and refresh table
        const modal = bootstrap.Modal.getInstance(document.getElementById('productModal'));
        modal.hide();
        applyFilters();
        form.classList.remove('was-validated');
    }

    // Edit product
    function editProduct(id) {
        openProductModal(id);
        const modal = new bootstrap.Modal(document.getElementById('productModal'));
        modal.show();
    }

    // View product (placeholder)
    function viewProduct(id) {
        const product = products.find(p => p.id === id);
        if (product) {
            Dashboard.showToast(`Viewing product: ${product.name}`, 'info');
            // In a real app, this would open a detailed view modal or navigate to a detail page
        }
    }

    // Delete product
    function deleteProduct(id) {
        const product = products.find(p => p.id === id);
        if (product && Dashboard.showConfirm(`Are you sure you want to delete "${product.name}"?`)) {
            products = products.filter(p => p.id !== id);
            applyFilters();
            Dashboard.showToast('Product deleted successfully!', 'success');
        }
    }

    // Export products
    function exportProducts() {
        const csvContent = convertToCSV(filteredProducts);
        downloadCSV(csvContent, 'products.csv');
        Dashboard.showToast('Products exported successfully!', 'success');
    }

    // Refresh products
    function refreshProducts() {
        // In a real app, this would fetch fresh data from the API
        applyFilters();
        Dashboard.showToast('Products refreshed!', 'info');
    }

    // Update bulk actions based on selected items
    function updateBulkActions() {
        const selectedCheckboxes = document.querySelectorAll('.product-checkbox:checked');
        const selectAllCheckbox = document.getElementById('selectAll');
        
        // Update select all checkbox state
        if (selectAllCheckbox) {
            const totalCheckboxes = document.querySelectorAll('.product-checkbox');
            selectAllCheckbox.indeterminate = selectedCheckboxes.length > 0 && selectedCheckboxes.length < totalCheckboxes.length;
            selectAllCheckbox.checked = selectedCheckboxes.length === totalCheckboxes.length && totalCheckboxes.length > 0;
        }

        // Show/hide bulk actions (if implemented)
        // const bulkActions = document.getElementById('bulkActions');
        // if (bulkActions) {
        //     bulkActions.style.display = selectedCheckboxes.length > 0 ? 'block' : 'none';
        // }
    }

    // Update product count display
    function updateProductCount() {
        const countElement = document.querySelector('.card-footer .text-muted');
        if (countElement) {
            const total = filteredProducts.length;
            countElement.textContent = `Showing 1 to ${total} of ${total} products`;
        }
    }

    // Utility functions
    function getCategoryColor(category) {
        const colors = {
            'subscription': 'primary',
            'service': 'info',
            'software': 'success'
        };
        return colors[category] || 'secondary';
    }

    function getStatusColor(status) {
        const colors = {
            'active': 'success',
            'inactive': 'secondary',
            'draft': 'warning'
        };
        return colors[status] || 'secondary';
    }

    function formatCategory(category) {
        return category.charAt(0).toUpperCase() + category.slice(1);
    }

    function formatStatus(status) {
        return status.charAt(0).toUpperCase() + status.slice(1);
    }

    function formatPrice(price) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(price);
    }

    function formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    function convertToCSV(data) {
        const headers = ['ID', 'Name', 'Description', 'Category', 'Price', 'Stock', 'Status', 'Featured', 'Created'];
        const csvRows = [headers.join(',')];
        
        data.forEach(product => {
            const row = [
                product.id,
                `"${product.name}"`,
                `"${product.description}"`,
                product.category,
                product.price,
                product.stock || 'Unlimited',
                product.status,
                product.featured,
                product.created
            ];
            csvRows.push(row.join(','));
        });
        
        return csvRows.join('\n');
    }

    function downloadCSV(csvContent, filename) {
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.setAttribute('hidden', '');
        a.setAttribute('href', url);
        a.setAttribute('download', filename);
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }

    // Export functions to global scope
    window.openProductModal = openProductModal;
    window.editProduct = editProduct;
    window.viewProduct = viewProduct;
    window.deleteProduct = deleteProduct;
    window.saveProduct = saveProduct;
    window.exportProducts = exportProducts;
    window.refreshProducts = refreshProducts;

})();
