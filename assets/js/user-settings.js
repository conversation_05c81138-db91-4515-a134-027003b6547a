// User Settings JavaScript
(function() {
    'use strict';

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        initializeUserSettings();
    });

    // Initialize user settings functionality
    function initializeUserSettings() {
        setupEventListeners();
        loadUserData();
    }

    // Setup event listeners
    function setupEventListeners() {
        // Form submissions
        const forms = [
            'profileForm',
            'passwordForm',
            'avatarForm',
            'notificationForm'
        ];

        forms.forEach(formId => {
            const form = document.getElementById(formId);
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    handleFormSubmission(formId);
                });
            }
        });

        // Avatar selection
        const avatarOptions = document.querySelectorAll('.avatar-option');
        avatarOptions.forEach(option => {
            option.addEventListener('click', function() {
                selectAvatar(this.dataset.avatar);
            });
        });

        // Avatar upload
        const avatarUpload = document.getElementById('avatarUpload');
        if (avatarUpload) {
            avatarUpload.addEventListener('change', function() {
                handleAvatarUpload(this);
            });
        }

        // Password validation
        const newPassword = document.getElementById('newPassword');
        const confirmPassword = document.getElementById('confirmPassword');
        
        if (newPassword && confirmPassword) {
            confirmPassword.addEventListener('input', function() {
                validatePasswordMatch();
            });
        }

        // Two-factor authentication toggle
        const twoFactorToggle = document.getElementById('twoFactorAuth');
        if (twoFactorToggle) {
            twoFactorToggle.addEventListener('change', function() {
                toggle2FA(this.checked);
            });
        }
    }

    // Load user data
    function loadUserData() {
        // In a real application, this would load user data from an API
        console.log('User data loaded');
    }

    // Handle form submissions
    function handleFormSubmission(formId) {
        switch (formId) {
            case 'profileForm':
                saveProfile();
                break;
            case 'passwordForm':
                changePassword();
                break;
            case 'avatarForm':
                saveAvatar();
                break;
            case 'notificationForm':
                saveNotificationPreferences();
                break;
        }
    }

    // Save profile information
    function saveProfile() {
        const profileData = {
            firstName: document.getElementById('firstName').value,
            lastName: document.getElementById('lastName').value,
            email: document.getElementById('email').value,
            phone: document.getElementById('phone').value,
            timezone: document.getElementById('timezone').value,
            bio: document.getElementById('bio').value,
            company: document.getElementById('company').value,
            jobTitle: document.getElementById('jobTitle').value
        };

        // Simulate API call
        setTimeout(() => {
            Dashboard.showToast('Profile updated successfully!', 'success');
            console.log('Saved profile:', profileData);
        }, 500);
    }

    // Change password
    function changePassword() {
        const currentPassword = document.getElementById('currentPassword').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        // Validate passwords match
        if (newPassword !== confirmPassword) {
            Dashboard.showToast('New passwords do not match', 'error');
            return;
        }

        // Validate password strength
        if (newPassword.length < 8) {
            Dashboard.showToast('Password must be at least 8 characters long', 'error');
            return;
        }

        // Simulate API call
        setTimeout(() => {
            Dashboard.showToast('Password updated successfully!', 'success');
            document.getElementById('passwordForm').reset();
        }, 500);
    }

    // Validate password match
    function validatePasswordMatch() {
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const confirmInput = document.getElementById('confirmPassword');

        if (confirmPassword && newPassword !== confirmPassword) {
            confirmInput.setCustomValidity('Passwords do not match');
        } else {
            confirmInput.setCustomValidity('');
        }
    }

    // Select avatar
    function selectAvatar(avatarPath) {
        const currentAvatar = document.getElementById('currentAvatar');
        if (currentAvatar) {
            currentAvatar.src = `../assets/images/avatars/${avatarPath}`;
        }

        // Update visual selection
        const avatarOptions = document.querySelectorAll('.avatar-option');
        avatarOptions.forEach(option => {
            option.classList.toggle('border-primary', option.dataset.avatar === avatarPath);
            option.classList.toggle('border-3', option.dataset.avatar === avatarPath);
        });

        Dashboard.showToast('Avatar selected', 'info');
    }

    // Handle avatar upload
    function handleAvatarUpload(input) {
        const file = input.files[0];
        if (!file) return;

        // Validate file size (2MB limit)
        if (file.size > 2 * 1024 * 1024) {
            Dashboard.showToast('File size must be less than 2MB', 'error');
            input.value = '';
            return;
        }

        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/png'];
        if (!allowedTypes.includes(file.type)) {
            Dashboard.showToast('Please select a JPG or PNG image', 'error');
            input.value = '';
            return;
        }

        // Preview the uploaded image
        const reader = new FileReader();
        reader.onload = function(e) {
            const currentAvatar = document.getElementById('currentAvatar');
            if (currentAvatar) {
                currentAvatar.src = e.target.result;
            }
        };
        reader.readAsDataURL(file);

        Dashboard.showToast('Avatar uploaded successfully!', 'success');
    }

    // Save avatar
    function saveAvatar() {
        // In a real application, this would upload the avatar to the server
        Dashboard.showToast('Avatar saved successfully!', 'success');
    }

    // Remove avatar
    function removeAvatar() {
        const currentAvatar = document.getElementById('currentAvatar');
        if (currentAvatar) {
            currentAvatar.src = '../assets/images/avatars/default-avatar.jpg';
        }

        // Clear file input
        const avatarUpload = document.getElementById('avatarUpload');
        if (avatarUpload) {
            avatarUpload.value = '';
        }

        // Clear avatar selection
        const avatarOptions = document.querySelectorAll('.avatar-option');
        avatarOptions.forEach(option => {
            option.classList.remove('border-primary', 'border-3');
        });

        Dashboard.showToast('Avatar removed', 'info');
    }

    // Save notification preferences
    function saveNotificationPreferences() {
        const preferences = {
            emailMarketing: document.getElementById('emailMarketing').checked,
            emailSecurity: document.getElementById('emailSecurity').checked,
            emailBilling: document.getElementById('emailBilling').checked,
            emailWeekly: document.getElementById('emailWeekly').checked,
            pushAlerts: document.getElementById('pushAlerts').checked,
            pushUpdates: document.getElementById('pushUpdates').checked,
            smsAlerts: document.getElementById('smsAlerts').checked
        };

        // Simulate API call
        setTimeout(() => {
            Dashboard.showToast('Notification preferences saved!', 'success');
            console.log('Saved preferences:', preferences);
        }, 500);
    }

    // Toggle 2FA
    function toggle2FA(enabled) {
        if (enabled) {
            Dashboard.showToast('Two-factor authentication enabled', 'success');
        } else {
            if (Dashboard.showConfirm('Are you sure you want to disable two-factor authentication? This will make your account less secure.')) {
                Dashboard.showToast('Two-factor authentication disabled', 'warning');
            } else {
                // Revert the toggle
                document.getElementById('twoFactorAuth').checked = true;
            }
        }
    }

    // Regenerate backup codes
    function regenerateBackupCodes() {
        if (Dashboard.showConfirm('Are you sure you want to regenerate backup codes? Your current codes will no longer work.')) {
            Dashboard.showToast('Backup codes regenerated successfully!', 'success');
            // In a real app, this would show the new codes
        }
    }

    // Disable 2FA
    function disable2FA() {
        if (Dashboard.showConfirm('Are you sure you want to disable two-factor authentication? This will make your account less secure.')) {
            document.getElementById('twoFactorAuth').checked = false;
            Dashboard.showToast('Two-factor authentication disabled', 'warning');
        }
    }

    // Revoke session
    function revokeSession(sessionType) {
        if (Dashboard.showConfirm(`Are you sure you want to revoke this ${sessionType} session?`)) {
            Dashboard.showToast(`${sessionType.charAt(0).toUpperCase() + sessionType.slice(1)} session revoked`, 'success');
            // In a real app, this would remove the session from the list
        }
    }

    // Revoke all sessions
    function revokeAllSessions() {
        if (Dashboard.showConfirm('Are you sure you want to sign out all other sessions? You will need to sign in again on those devices.')) {
            Dashboard.showToast('All other sessions have been revoked', 'success');
            // In a real app, this would update the session list
        }
    }

    // Change plan
    function changePlan() {
        Dashboard.showToast('Redirecting to plan selection...', 'info');
        // In a real app, this would redirect to the billing page
    }

    // View billing history
    function viewBillingHistory() {
        Dashboard.showToast('Opening billing history...', 'info');
        // In a real app, this would open a billing history modal or page
    }

    // Update payment method
    function updatePaymentMethod() {
        Dashboard.showToast('Opening payment method update...', 'info');
        // In a real app, this would open a payment method modal
    }

    // Export data
    function exportData() {
        Dashboard.showToast('Data export request submitted. You will receive an email when ready.', 'info');
        // In a real app, this would initiate a data export process
    }

    // Save privacy settings
    function savePrivacySettings() {
        const privacySettings = {
            profilePublic: document.getElementById('profilePublic').checked,
            analyticsTracking: document.getElementById('analyticsTracking').checked,
            thirdPartySharing: document.getElementById('thirdPartySharing').checked
        };

        // Simulate API call
        setTimeout(() => {
            Dashboard.showToast('Privacy settings saved!', 'success');
            console.log('Saved privacy settings:', privacySettings);
        }, 500);
    }

    // Deactivate account
    function deactivateAccount() {
        if (Dashboard.showConfirm('Are you sure you want to deactivate your account? You can reactivate it anytime by signing in.')) {
            Dashboard.showToast('Account deactivated. You will be signed out shortly.', 'warning');
            // In a real app, this would deactivate the account and sign out the user
        }
    }

    // Delete account
    function deleteAccount() {
        const confirmation = prompt('Type "DELETE" to confirm account deletion:');
        if (confirmation === 'DELETE') {
            if (Dashboard.showConfirm('This action cannot be undone. Are you absolutely sure you want to delete your account permanently?')) {
                Dashboard.showToast('Account deletion initiated. You will receive a confirmation email.', 'error');
                // In a real app, this would initiate the account deletion process
            }
        } else if (confirmation !== null) {
            Dashboard.showToast('Account deletion cancelled - confirmation text did not match', 'info');
        }
    }

    // Export functions to global scope
    window.removeAvatar = removeAvatar;
    window.regenerateBackupCodes = regenerateBackupCodes;
    window.disable2FA = disable2FA;
    window.revokeSession = revokeSession;
    window.revokeAllSessions = revokeAllSessions;
    window.changePlan = changePlan;
    window.viewBillingHistory = viewBillingHistory;
    window.updatePaymentMethod = updatePaymentMethod;
    window.exportData = exportData;
    window.savePrivacySettings = savePrivacySettings;
    window.deactivateAccount = deactivateAccount;
    window.deleteAccount = deleteAccount;

})();
