/* Landing Page Styles */
body {
  font-family: var(--font-family-primary);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
}

/* Navigation */
.navbar {
  height: var(--navbar-height);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: var(--transition-normal);
}

.navbar-brand {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  text-decoration: none;
}

.navbar-nav .nav-link {
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  transition: var(--transition-fast);
  padding: 0.5rem 1rem;
}

.navbar-nav .nav-link:hover {
  color: var(--primary-color);
}

/* Hero Section */
.hero-section {
  background: var(--hero-bg);
  padding-top: var(--navbar-height);
  display: flex;
  align-items: center;
}

.hero-content h1 {
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-lg);
}

.hero-content .lead {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  color: var(--text-secondary);
}

.hero-actions {
  margin-bottom: var(--spacing-2xl);
}

.hero-stats .stat-item h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-xs);
}

.hero-stats .stat-item p {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-bottom: 0;
}

.hero-image img {
  max-width: 100%;
  height: auto;
  filter: drop-shadow(var(--shadow-lg));
}

/* Features Section */
.features-section {
  padding: var(--spacing-5xl) 0;
}

.feature-card {
  background: var(--feature-card-bg);
  border: 1px solid var(--feature-card-border);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-2xl);
  text-align: center;
  transition: var(--transition-normal);
  box-shadow: var(--feature-card-shadow);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--feature-card-hover-shadow);
}

.feature-icon {
  width: 64px;
  height: 64px;
  background: var(--feature-icon-bg);
  border-radius: var(--border-radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-lg);
}

.feature-icon .material-icons-outlined {
  font-size: 32px;
  color: var(--feature-icon-color);
}

.feature-card h4 {
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-md);
}

.feature-card p {
  color: var(--text-secondary);
  margin-bottom: 0;
}

/* Pricing Section */
.pricing-section {
  padding: var(--spacing-5xl) 0;
}

.pricing-card {
  background: var(--pricing-card-bg);
  border: 2px solid var(--pricing-card-border);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-2xl);
  text-align: center;
  transition: var(--transition-normal);
  box-shadow: var(--pricing-card-shadow);
  position: relative;
  height: 100%;
}

.pricing-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--pricing-card-hover-shadow);
}

.pricing-card.featured {
  border-color: var(--pricing-featured-border);
  transform: scale(1.05);
}

.pricing-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--pricing-badge-bg);
  color: var(--pricing-badge-text);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.pricing-header {
  margin-bottom: var(--spacing-xl);
}

.pricing-header h4 {
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-md);
}

.price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: var(--spacing-md);
}

.price .currency {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

.price .amount {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 var(--spacing-xs);
}

.price .period {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
}

.pricing-features {
  list-style: none;
  padding: 0;
  margin-bottom: var(--spacing-xl);
}

.pricing-features li {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) 0;
  color: var(--text-secondary);
}

.pricing-features li .material-icons-outlined {
  color: var(--success-color);
  margin-right: var(--spacing-sm);
  font-size: 20px;
}

/* FAQ Section */
.faq-section {
  padding: var(--spacing-5xl) 0;
}

.accordion-item {
  border: 1px solid var(--border-color);
  margin-bottom: var(--spacing-sm);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.accordion-button {
  font-weight: var(--font-weight-medium);
  background: var(--white);
  border: none;
  padding: var(--spacing-lg);
}

.accordion-button:not(.collapsed) {
  background: var(--primary-light);
  color: var(--primary-dark);
}

.accordion-button:focus {
  box-shadow: none;
  border-color: var(--primary-color);
}

.accordion-body {
  padding: var(--spacing-lg);
  color: var(--text-secondary);
}

/* CTA Section */
.cta-section {
  padding: var(--spacing-5xl) 0;
}

.cta-section h2 {
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-lg);
}

.cta-section .lead {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-xl);
  opacity: 0.9;
}

/* Footer */
.footer {
  background: var(--footer-bg);
  color: var(--footer-text);
}

.footer-brand img {
  filter: brightness(0) invert(1);
}

.footer h6 {
  color: var(--white);
  font-weight: var(--font-weight-semibold);
}

.footer a {
  color: var(--footer-text);
  transition: var(--transition-fast);
}

.footer a:hover {
  color: var(--footer-link-hover);
}

.social-links a {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-full);
  transition: var(--transition-fast);
}

.social-links a:hover {
  background: var(--gray-800);
  color: var(--white);
}

/* Modal Styles */
.modal-content {
  background: var(--modal-bg);
  border-radius: var(--modal-border-radius);
  box-shadow: var(--modal-shadow);
  border: none;
}

.modal-header {
  padding: var(--spacing-xl) var(--spacing-xl) 0;
}

.modal-body {
  padding: var(--spacing-lg) var(--spacing-xl);
}

.modal-footer {
  padding: 0 var(--spacing-xl) var(--spacing-xl);
}

.modal-title {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

/* Form Styles */
.form-control {
  background: var(--form-control-bg);
  border: 1px solid var(--form-control-border);
  border-radius: var(--border-radius);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-base);
  transition: var(--transition-fast);
}

.form-control:focus {
  border-color: var(--form-control-focus-border);
  box-shadow: var(--form-control-focus-shadow);
}

.form-label {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

/* Button Styles */
.btn {
  font-weight: var(--btn-font-weight);
  border-radius: var(--btn-border-radius);
  transition: var(--btn-transition);
  padding: var(--spacing-sm) var(--spacing-lg);
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    text-align: center;
  }
  
  .hero-content h1 {
    font-size: var(--font-size-4xl);
  }
  
  .hero-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .hero-actions .btn {
    width: 100%;
  }
  
  .pricing-card.featured {
    transform: none;
    margin-top: var(--spacing-lg);
  }
  
  .feature-card,
  .pricing-card {
    margin-bottom: var(--spacing-lg);
  }
}

@media (max-width: 576px) {
  .hero-content h1 {
    font-size: var(--font-size-3xl);
  }
  
  .display-5 {
    font-size: var(--font-size-3xl);
  }
  
  .lead {
    font-size: var(--font-size-base);
  }
  
  .feature-card,
  .pricing-card {
    padding: var(--spacing-lg);
  }
}

/* Utility Classes */
.shadow-custom {
  box-shadow: var(--shadow);
}

.shadow-custom-lg {
  box-shadow: var(--shadow-lg);
}

.border-radius-custom {
  border-radius: var(--border-radius);
}

.border-radius-custom-lg {
  border-radius: var(--border-radius-lg);
}

.transition-custom {
  transition: var(--transition-normal);
}
