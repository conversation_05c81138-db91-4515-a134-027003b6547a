/* Landing Page Default Theme - CSS Variables */
:root {
  /* Primary Colors */
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --primary-light: #dbeafe;
  --primary-dark: #1d4ed8;
  
  /* Secondary Colors */
  --secondary-color: #64748b;
  --secondary-hover: #475569;
  --secondary-light: #f1f5f9;
  --secondary-dark: #334155;
  
  /* Success Colors */
  --success-color: #10b981;
  --success-hover: #059669;
  --success-light: #d1fae5;
  --success-dark: #047857;
  
  /* Warning Colors */
  --warning-color: #f59e0b;
  --warning-hover: #d97706;
  --warning-light: #fef3c7;
  --warning-dark: #b45309;
  
  /* Danger Colors */
  --danger-color: #ef4444;
  --danger-hover: #dc2626;
  --danger-light: #fee2e2;
  --danger-dark: #b91c1c;
  
  /* Info Colors */
  --info-color: #06b6d4;
  --info-hover: #0891b2;
  --info-light: #cffafe;
  --info-dark: #0e7490;
  
  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  --black: #000000;
  
  /* Background Colors */
  --bg-primary: var(--white);
  --bg-secondary: var(--gray-50);
  --bg-tertiary: var(--gray-100);
  --bg-dark: var(--gray-900);
  
  /* Text Colors */
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-600);
  --text-muted: var(--gray-500);
  --text-light: var(--gray-400);
  --text-white: var(--white);
  
  /* Border Colors */
  --border-color: var(--gray-200);
  --border-light: var(--gray-100);
  --border-dark: var(--gray-300);
  
  /* Shadow Colors */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  /* Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', Consolas, 'Courier New', monospace;
  
  /* Font Sizes */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;
  
  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  --spacing-4xl: 6rem;
  --spacing-5xl: 8rem;
  
  /* Border Radius */
  --border-radius-sm: 0.25rem;
  --border-radius: 0.375rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  --border-radius-2xl: 1.5rem;
  --border-radius-full: 9999px;
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
  
  /* Z-Index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  
  /* Component Specific Variables */
  --navbar-height: 70px;
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 80px;
  
  /* Hero Section */
  --hero-bg: linear-gradient(135deg, var(--primary-light) 0%, var(--white) 100%);
  --hero-text-primary: var(--text-primary);
  --hero-text-secondary: var(--text-secondary);
  
  /* Feature Cards */
  --feature-card-bg: var(--white);
  --feature-card-border: var(--border-color);
  --feature-card-shadow: var(--shadow);
  --feature-card-hover-shadow: var(--shadow-lg);
  --feature-icon-bg: var(--primary-light);
  --feature-icon-color: var(--primary-color);
  
  /* Pricing Cards */
  --pricing-card-bg: var(--white);
  --pricing-card-border: var(--border-color);
  --pricing-card-shadow: var(--shadow);
  --pricing-card-hover-shadow: var(--shadow-lg);
  --pricing-featured-border: var(--primary-color);
  --pricing-badge-bg: var(--primary-color);
  --pricing-badge-text: var(--white);
  
  /* Buttons */
  --btn-border-radius: var(--border-radius);
  --btn-font-weight: var(--font-weight-medium);
  --btn-transition: var(--transition-fast);
  
  /* Forms */
  --form-control-bg: var(--white);
  --form-control-border: var(--border-color);
  --form-control-focus-border: var(--primary-color);
  --form-control-focus-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
  
  /* Cards */
  --card-bg: var(--white);
  --card-border: var(--border-color);
  --card-shadow: var(--shadow-sm);
  --card-border-radius: var(--border-radius-lg);
  
  /* Modals */
  --modal-bg: var(--white);
  --modal-border-radius: var(--border-radius-lg);
  --modal-shadow: var(--shadow-xl);
  
  /* Footer */
  --footer-bg: var(--gray-900);
  --footer-text: var(--gray-400);
  --footer-link-hover: var(--white);
}

/* Bootstrap Variable Overrides */
.btn-primary {
  --bs-btn-bg: var(--primary-color);
  --bs-btn-border-color: var(--primary-color);
  --bs-btn-hover-bg: var(--primary-hover);
  --bs-btn-hover-border-color: var(--primary-hover);
  --bs-btn-active-bg: var(--primary-dark);
  --bs-btn-active-border-color: var(--primary-dark);
}

.btn-secondary {
  --bs-btn-bg: var(--secondary-color);
  --bs-btn-border-color: var(--secondary-color);
  --bs-btn-hover-bg: var(--secondary-hover);
  --bs-btn-hover-border-color: var(--secondary-hover);
  --bs-btn-active-bg: var(--secondary-dark);
  --bs-btn-active-border-color: var(--secondary-dark);
}

.btn-success {
  --bs-btn-bg: var(--success-color);
  --bs-btn-border-color: var(--success-color);
  --bs-btn-hover-bg: var(--success-hover);
  --bs-btn-hover-border-color: var(--success-hover);
  --bs-btn-active-bg: var(--success-dark);
  --bs-btn-active-border-color: var(--success-dark);
}

.btn-warning {
  --bs-btn-bg: var(--warning-color);
  --bs-btn-border-color: var(--warning-color);
  --bs-btn-hover-bg: var(--warning-hover);
  --bs-btn-hover-border-color: var(--warning-hover);
  --bs-btn-active-bg: var(--warning-dark);
  --bs-btn-active-border-color: var(--warning-dark);
}

.btn-danger {
  --bs-btn-bg: var(--danger-color);
  --bs-btn-border-color: var(--danger-color);
  --bs-btn-hover-bg: var(--danger-hover);
  --bs-btn-hover-border-color: var(--danger-hover);
  --bs-btn-active-bg: var(--danger-dark);
  --bs-btn-active-border-color: var(--danger-dark);
}

.btn-info {
  --bs-btn-bg: var(--info-color);
  --bs-btn-border-color: var(--info-color);
  --bs-btn-hover-bg: var(--info-hover);
  --bs-btn-hover-border-color: var(--info-hover);
  --bs-btn-active-bg: var(--info-dark);
  --bs-btn-active-border-color: var(--info-dark);
}

.text-primary {
  color: var(--primary-color) !important;
}

.text-secondary {
  color: var(--secondary-color) !important;
}

.text-success {
  color: var(--success-color) !important;
}

.text-warning {
  color: var(--warning-color) !important;
}

.text-danger {
  color: var(--danger-color) !important;
}

.text-info {
  color: var(--info-color) !important;
}

.text-muted {
  color: var(--text-muted) !important;
}

.bg-primary {
  background-color: var(--primary-color) !important;
}

.bg-secondary {
  background-color: var(--secondary-color) !important;
}

.bg-success {
  background-color: var(--success-color) !important;
}

.bg-warning {
  background-color: var(--warning-color) !important;
}

.bg-danger {
  background-color: var(--danger-color) !important;
}

.bg-info {
  background-color: var(--info-color) !important;
}

.bg-light {
  background-color: var(--gray-100) !important;
}

.bg-dark {
  background-color: var(--gray-900) !important;
}
