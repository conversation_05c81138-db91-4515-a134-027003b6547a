/* Landing Page Dark Theme - CSS Variables */
:root {
  /* Primary Colors - Purple/Violet Theme */
  --primary-color: #8b5cf6;
  --primary-hover: #7c3aed;
  --primary-light: #ede9fe;
  --primary-dark: #6d28d9;
  
  /* Secondary Colors */
  --secondary-color: #6b7280;
  --secondary-hover: #4b5563;
  --secondary-light: #f3f4f6;
  --secondary-dark: #374151;
  
  /* Success Colors */
  --success-color: #10b981;
  --success-hover: #059669;
  --success-light: #d1fae5;
  --success-dark: #047857;
  
  /* Warning Colors */
  --warning-color: #f59e0b;
  --warning-hover: #d97706;
  --warning-light: #fef3c7;
  --warning-dark: #b45309;
  
  /* Danger Colors */
  --danger-color: #ef4444;
  --danger-hover: #dc2626;
  --danger-light: #fee2e2;
  --danger-dark: #b91c1c;
  
  /* Info Colors */
  --info-color: #06b6d4;
  --info-hover: #0891b2;
  --info-light: #cffafe;
  --info-dark: #0e7490;
  
  /* Dark Theme Neutral Colors */
  --white: #ffffff;
  --gray-50: #1f2937;
  --gray-100: #111827;
  --gray-200: #374151;
  --gray-300: #4b5563;
  --gray-400: #6b7280;
  --gray-500: #9ca3af;
  --gray-600: #d1d5db;
  --gray-700: #e5e7eb;
  --gray-800: #f3f4f6;
  --gray-900: #f9fafb;
  --black: #000000;
  
  /* Background Colors - Dark Theme */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-dark: #0f172a;
  
  /* Text Colors - Dark Theme */
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --text-light: #64748b;
  --text-white: #ffffff;
  
  /* Border Colors - Dark Theme */
  --border-color: #334155;
  --border-light: #1e293b;
  --border-dark: #475569;
  
  /* Shadow Colors - Dark Theme */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.4);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);
  
  /* Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', Consolas, 'Courier New', monospace;
  
  /* Font Sizes */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;
  
  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  --spacing-4xl: 6rem;
  --spacing-5xl: 8rem;
  
  /* Border Radius */
  --border-radius-sm: 0.25rem;
  --border-radius: 0.375rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  --border-radius-2xl: 1.5rem;
  --border-radius-full: 9999px;
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
  
  /* Z-Index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  
  /* Component Specific Variables */
  --navbar-height: 70px;
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 80px;
  
  /* Hero Section - Dark Theme */
  --hero-bg: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  --hero-text-primary: var(--text-primary);
  --hero-text-secondary: var(--text-secondary);
  
  /* Feature Cards - Dark Theme */
  --feature-card-bg: var(--bg-secondary);
  --feature-card-border: var(--border-color);
  --feature-card-shadow: var(--shadow);
  --feature-card-hover-shadow: var(--shadow-lg);
  --feature-icon-bg: rgba(139, 92, 246, 0.1);
  --feature-icon-color: var(--primary-color);
  
  /* Pricing Cards - Dark Theme */
  --pricing-card-bg: var(--bg-secondary);
  --pricing-card-border: var(--border-color);
  --pricing-card-shadow: var(--shadow);
  --pricing-card-hover-shadow: var(--shadow-lg);
  --pricing-featured-border: var(--primary-color);
  --pricing-badge-bg: var(--primary-color);
  --pricing-badge-text: var(--white);
  
  /* Buttons */
  --btn-border-radius: var(--border-radius);
  --btn-font-weight: var(--font-weight-medium);
  --btn-transition: var(--transition-fast);
  
  /* Forms - Dark Theme */
  --form-control-bg: var(--bg-tertiary);
  --form-control-border: var(--border-color);
  --form-control-focus-border: var(--primary-color);
  --form-control-focus-shadow: 0 0 0 0.2rem rgba(139, 92, 246, 0.25);
  
  /* Cards - Dark Theme */
  --card-bg: var(--bg-secondary);
  --card-border: var(--border-color);
  --card-shadow: var(--shadow-sm);
  --card-border-radius: var(--border-radius-lg);
  
  /* Modals - Dark Theme */
  --modal-bg: var(--bg-secondary);
  --modal-border-radius: var(--border-radius-lg);
  --modal-shadow: var(--shadow-xl);
  
  /* Footer - Dark Theme */
  --footer-bg: var(--bg-primary);
  --footer-text: var(--text-muted);
  --footer-link-hover: var(--text-primary);
}

/* Dark Theme Body Override */
body {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

/* Bootstrap Variable Overrides for Dark Theme */
.btn-primary {
  --bs-btn-bg: var(--primary-color);
  --bs-btn-border-color: var(--primary-color);
  --bs-btn-hover-bg: var(--primary-hover);
  --bs-btn-hover-border-color: var(--primary-hover);
  --bs-btn-active-bg: var(--primary-dark);
  --bs-btn-active-border-color: var(--primary-dark);
}

.btn-secondary {
  --bs-btn-bg: var(--secondary-color);
  --bs-btn-border-color: var(--secondary-color);
  --bs-btn-hover-bg: var(--secondary-hover);
  --bs-btn-hover-border-color: var(--secondary-hover);
  --bs-btn-active-bg: var(--secondary-dark);
  --bs-btn-active-border-color: var(--secondary-dark);
}

.btn-outline-secondary {
  --bs-btn-color: var(--text-secondary);
  --bs-btn-border-color: var(--border-color);
  --bs-btn-hover-bg: var(--bg-tertiary);
  --bs-btn-hover-color: var(--text-primary);
  --bs-btn-hover-border-color: var(--border-dark);
}

.btn-outline-primary {
  --bs-btn-color: var(--primary-color);
  --bs-btn-border-color: var(--primary-color);
  --bs-btn-hover-bg: var(--primary-color);
  --bs-btn-hover-color: var(--white);
}

.text-primary {
  color: var(--primary-color) !important;
}

.text-secondary {
  color: var(--text-secondary) !important;
}

.text-muted {
  color: var(--text-muted) !important;
}

.bg-primary {
  background-color: var(--primary-color) !important;
}

.bg-secondary {
  background-color: var(--bg-secondary) !important;
}

.bg-light {
  background-color: var(--bg-secondary) !important;
}

.bg-dark {
  background-color: var(--bg-primary) !important;
}

.bg-white {
  background-color: var(--bg-secondary) !important;
}

/* Navbar Dark Theme */
.navbar {
  background-color: var(--bg-secondary) !important;
  border-bottom: 1px solid var(--border-color);
}

.navbar-brand {
  color: var(--text-primary) !important;
}

.navbar-nav .nav-link {
  color: var(--text-secondary) !important;
}

.navbar-nav .nav-link:hover {
  color: var(--primary-color) !important;
}

.navbar-nav .nav-link.active {
  color: var(--primary-color) !important;
}

/* Card Dark Theme */
.card {
  background-color: var(--card-bg) !important;
  border-color: var(--card-border) !important;
  color: var(--text-primary) !important;
}

/* Modal Dark Theme */
.modal-content {
  background-color: var(--modal-bg) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

.modal-header {
  border-bottom-color: var(--border-color) !important;
}

.modal-footer {
  border-top-color: var(--border-color) !important;
}

/* Form Controls Dark Theme */
.form-control {
  background-color: var(--form-control-bg) !important;
  border-color: var(--form-control-border) !important;
  color: var(--text-primary) !important;
}

.form-control:focus {
  background-color: var(--form-control-bg) !important;
  border-color: var(--form-control-focus-border) !important;
  box-shadow: var(--form-control-focus-shadow) !important;
  color: var(--text-primary) !important;
}

.form-control::placeholder {
  color: var(--text-muted) !important;
}

.form-label {
  color: var(--text-primary) !important;
}

.form-select {
  background-color: var(--form-control-bg) !important;
  border-color: var(--form-control-border) !important;
  color: var(--text-primary) !important;
}

/* Accordion Dark Theme */
.accordion-item {
  background-color: var(--card-bg) !important;
  border-color: var(--border-color) !important;
}

.accordion-button {
  background-color: var(--card-bg) !important;
  color: var(--text-primary) !important;
}

.accordion-button:not(.collapsed) {
  background-color: var(--bg-tertiary) !important;
  color: var(--primary-color) !important;
}

.accordion-body {
  background-color: var(--card-bg) !important;
  color: var(--text-secondary) !important;
}

/* Footer Dark Theme */
.footer {
  background-color: var(--footer-bg) !important;
  color: var(--footer-text) !important;
}

.footer h6 {
  color: var(--text-primary) !important;
}

.footer a {
  color: var(--footer-text) !important;
}

.footer a:hover {
  color: var(--footer-link-hover) !important;
}

/* Additional Dark Theme Overrides */
.text-dark {
  color: var(--text-primary) !important;
}

.border {
  border-color: var(--border-color) !important;
}

.shadow-sm {
  box-shadow: var(--shadow-sm) !important;
}

.shadow {
  box-shadow: var(--shadow) !important;
}

.shadow-lg {
  box-shadow: var(--shadow-lg) !important;
}

/* Toast Dark Theme */
.toast {
  background-color: var(--bg-secondary) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

/* Dropdown Dark Theme */
.dropdown-menu {
  background-color: var(--bg-secondary) !important;
  border-color: var(--border-color) !important;
}

.dropdown-item {
  color: var(--text-secondary) !important;
}

.dropdown-item:hover {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
}

/* Badge Dark Theme */
.badge.bg-light {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
}

/* Pagination Dark Theme */
.page-link {
  background-color: var(--bg-secondary) !important;
  border-color: var(--border-color) !important;
  color: var(--text-secondary) !important;
}

.page-link:hover {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--border-dark) !important;
  color: var(--text-primary) !important;
}

.page-item.active .page-link {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: var(--white) !important;
}
