/* Dashboard Styles */
body {
  font-family: var(--font-family-primary);
  line-height: var(--line-height-normal);
  color: var(--content-text);
  background-color: var(--content-bg);
  overflow-x: hidden;
}

/* Dashboard Layout */
.dashboard-wrapper {
  display: flex;
  min-height: 100vh;
}

/* Sidebar */
.sidebar {
  width: var(--sidebar-width);
  background: var(--sidebar-bg);
  border-right: 1px solid var(--sidebar-border);
  box-shadow: var(--sidebar-shadow);
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: var(--z-fixed);
  transition: var(--transition-normal);
  overflow-y: auto;
  overflow-x: hidden;
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-brand {
  background: var(--sidebar-brand-bg);
  color: var(--sidebar-brand-text);
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  text-decoration: none;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
  min-height: var(--navbar-height);
}

.sidebar-brand img {
  width: 32px;
  height: 32px;
  margin-right: var(--spacing-sm);
  filter: brightness(0) invert(1);
}

.sidebar.collapsed .sidebar-brand-text {
  display: none;
}

.sidebar-nav {
  padding: var(--spacing-lg) 0;
}

.sidebar-nav-item {
  margin-bottom: var(--spacing-xs);
}

.sidebar-nav-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  color: var(--sidebar-nav-text);
  text-decoration: none;
  transition: var(--transition-fast);
  position: relative;
  border-radius: 0;
}

.sidebar-nav-link:hover {
  background: var(--sidebar-nav-hover-bg);
  color: var(--sidebar-nav-hover-text);
}

.sidebar-nav-link.active {
  background: var(--sidebar-nav-active-bg);
  color: var(--sidebar-nav-active-text);
  border-right: 3px solid var(--sidebar-nav-active-border);
}

.sidebar-nav-icon {
  width: 20px;
  height: 20px;
  margin-right: var(--spacing-sm);
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar.collapsed .sidebar-nav-text {
  display: none;
}

.sidebar.collapsed .sidebar-nav-link {
  justify-content: center;
  padding: var(--spacing-sm);
}

.sidebar.collapsed .sidebar-nav-icon {
  margin-right: 0;
}

/* Sidebar Toggle */
.sidebar-toggle {
  position: absolute;
  top: 50%;
  right: -12px;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast);
  z-index: 1;
}

.sidebar-toggle:hover {
  background: var(--primary-hover);
}

.sidebar-toggle .material-icons-outlined {
  font-size: 16px;
}

/* Main Content */
.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  transition: var(--transition-normal);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.sidebar.collapsed + .main-content {
  margin-left: var(--sidebar-collapsed-width);
}

/* Top Navigation */
.topnav {
  background: var(--topnav-bg);
  border-bottom: 1px solid var(--topnav-border);
  box-shadow: var(--topnav-shadow);
  padding: 0 var(--content-padding);
  height: var(--navbar-height);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.topnav-left {
  display: flex;
  align-items: center;
}

.topnav-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--topnav-text);
  margin: 0;
}

.topnav-breadcrumb {
  color: var(--topnav-text-muted);
  font-size: var(--font-size-sm);
  margin-left: var(--spacing-sm);
}

.topnav-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* Search Bar */
.topnav-search {
  position: relative;
  width: 300px;
}

.topnav-search .form-control {
  padding-left: 2.5rem;
  border-radius: var(--border-radius-full);
  border: 1px solid var(--border-color);
  background: var(--form-control-bg);
}

.topnav-search .search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: 18px;
}

/* Notifications */
.notification-dropdown {
  position: relative;
}

.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: var(--danger-color);
  color: var(--white);
  border-radius: var(--border-radius-full);
  width: 18px;
  height: 18px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
}

/* User Dropdown */
.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  transition: var(--transition-fast);
}

.user-dropdown:hover {
  background: var(--gray-100);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius-full);
  margin-right: var(--spacing-sm);
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  line-height: 1.2;
}

.user-role {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  line-height: 1.2;
}

/* Content Area */
.content {
  flex: 1;
  padding: var(--content-padding);
}

.content-header {
  margin-bottom: var(--spacing-xl);
}

.content-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.content-subtitle {
  color: var(--text-muted);
  font-size: var(--font-size-base);
}

/* Cards */
.card {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--card-border-radius);
  box-shadow: var(--card-shadow);
  transition: var(--transition-normal);
}

.card:hover {
  box-shadow: var(--card-hover-shadow);
}

.card-header {
  background: var(--card-header-bg);
  border-bottom: 1px solid var(--card-header-border);
  padding: var(--spacing-lg);
  border-radius: var(--card-border-radius) var(--card-border-radius) 0 0;
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.card-body {
  padding: var(--spacing-lg);
}

/* Stats Cards */
.stats-card {
  background: var(--stats-card-bg);
  border: 1px solid var(--stats-card-border);
  border-radius: var(--card-border-radius);
  box-shadow: var(--stats-card-shadow);
  padding: var(--spacing-lg);
  transition: var(--transition-normal);
}

.stats-card:hover {
  box-shadow: var(--stats-card-hover-shadow);
  transform: translateY(-2px);
}

.stats-card-icon {
  width: 48px;
  height: 48px;
  background: var(--stats-icon-bg);
  color: var(--stats-icon-color);
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-md);
}

.stats-card-icon .material-icons-outlined {
  font-size: 24px;
}

.stats-card-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.stats-card-label {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-sm);
}

.stats-card-change {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
}

.stats-card-change.positive {
  color: var(--success-color);
}

.stats-card-change.negative {
  color: var(--danger-color);
}

.stats-card-change .material-icons-outlined {
  font-size: 16px;
  margin-right: var(--spacing-xs);
}

/* Tables */
.table {
  background: var(--table-bg);
  border-color: var(--table-border);
}

.table thead th {
  background: var(--table-header-bg);
  color: var(--table-header-text);
  font-weight: var(--font-weight-semibold);
  border-color: var(--table-border);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table tbody tr:hover {
  background: var(--table-row-hover-bg);
}

.table-striped tbody tr:nth-of-type(odd) {
  background: var(--table-stripe-bg);
}

/* Buttons */
.btn {
  font-weight: var(--btn-font-weight);
  border-radius: var(--btn-border-radius);
  transition: var(--btn-transition);
  border-width: 1px;
}

.btn-sm {
  font-size: var(--font-size-sm);
  padding: 0.375rem 0.75rem;
}

.btn-lg {
  font-size: var(--font-size-lg);
  padding: 0.75rem 1.5rem;
}

/* Form Controls */
.form-control {
  background: var(--form-control-bg);
  border: 1px solid var(--form-control-border);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  font-size: var(--font-size-base);
  transition: var(--transition-fast);
}

.form-control:focus {
  border-color: var(--form-control-focus-border);
  box-shadow: var(--form-control-focus-shadow);
  background: var(--form-control-bg);
  color: var(--text-primary);
}

.form-control:disabled {
  background: var(--form-control-disabled-bg);
  color: var(--form-control-disabled-text);
}

.form-label {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

/* Badges */
.badge {
  border-radius: var(--badge-border-radius);
  font-size: var(--badge-font-size);
  font-weight: var(--badge-font-weight);
  padding: 0.375em 0.75em;
}

/* Status Indicators */
.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: var(--border-radius-full);
  display: inline-block;
  margin-right: var(--spacing-xs);
}

.status-online { background: var(--status-online); }
.status-offline { background: var(--status-offline); }
.status-away { background: var(--status-away); }
.status-busy { background: var(--status-busy); }

/* Responsive Design */
@media (max-width: 1200px) {
  .topnav-search {
    width: 250px;
  }
}

@media (max-width: 992px) {
  .sidebar {
    transform: translateX(-100%);
  }
  
  .sidebar.show {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .sidebar.collapsed + .main-content {
    margin-left: 0;
  }
  
  .topnav-search {
    display: none;
  }
  
  .user-info {
    display: none;
  }
}

@media (max-width: 768px) {
  .content {
    padding: var(--spacing-md);
  }
  
  .content-title {
    font-size: var(--font-size-xl);
  }
  
  .topnav {
    padding: 0 var(--spacing-md);
  }
  
  .topnav-right {
    gap: var(--spacing-sm);
  }
}

@media (max-width: 576px) {
  .content {
    padding: var(--spacing-sm);
  }
  
  .card-body {
    padding: var(--spacing-md);
  }
  
  .stats-card {
    padding: var(--spacing-md);
  }
  
  .stats-card-value {
    font-size: var(--font-size-xl);
  }
}

/* Mobile Sidebar Overlay */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: calc(var(--z-fixed) - 1);
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-normal);
}

.sidebar-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* Utility Classes */
.shadow-custom {
  box-shadow: var(--shadow);
}

.shadow-custom-lg {
  box-shadow: var(--shadow-lg);
}

.border-radius-custom {
  border-radius: var(--border-radius);
}

.border-radius-custom-lg {
  border-radius: var(--border-radius-lg);
}

.transition-custom {
  transition: var(--transition-normal);
}
