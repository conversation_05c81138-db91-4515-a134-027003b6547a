<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Test - SaaSShip</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5">
        <h1 class="mb-4">SaaSShip Image Test</h1>
        
        <div class="row g-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Logo & Branding</h5>
                    </div>
                    <div class="card-body">
                        <img src="assets/images/logo.svg" alt="Logo" height="50" class="mb-3">
                        <br>
                        <img src="assets/images/favicon.ico" alt="Favicon" width="32" height="32">
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Hero Image</h5>
                    </div>
                    <div class="card-body">
                        <img src="https://placehold.co/600x400/3B82F6/FFFFFF?text=SaaSShip+Dashboard" alt="Hero" class="img-fluid rounded">
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>User Avatars</h5>
                    </div>
                    <div class="card-body">
                        <img src="https://placehold.co/60x60/3B82F6/FFFFFF?text=JD" alt="User 1" class="rounded-circle me-2" width="60" height="60">
                        <img src="https://placehold.co/60x60/EF4444/FFFFFF?text=JS" alt="User 2" class="rounded-circle me-2" width="60" height="60">
                        <img src="https://placehold.co/60x60/10B981/FFFFFF?text=MJ" alt="User 3" class="rounded-circle me-2" width="60" height="60">
                        <img src="https://placehold.co/60x60/F59E0B/FFFFFF?text=SW" alt="User 4" class="rounded-circle" width="60" height="60">
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Product Images</h5>
                    </div>
                    <div class="card-body">
                        <img src="https://placehold.co/80x60/3B82F6/FFFFFF?text=S" alt="Starter" class="rounded me-2" width="80" height="60">
                        <img src="https://placehold.co/80x60/10B981/FFFFFF?text=P" alt="Pro" class="rounded me-2" width="80" height="60">
                        <img src="https://placehold.co/80x60/F59E0B/FFFFFF?text=E" alt="Enterprise" class="rounded me-2" width="80" height="60">
                        <img src="https://placehold.co/80x60/8B5CF6/FFFFFF?text=API" alt="API" class="rounded" width="80" height="60">
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Blog Images</h5>
                    </div>
                    <div class="card-body">
                        <img src="https://placehold.co/200x120/3B82F6/FFFFFF?text=Getting+Started" alt="Blog 1" class="img-fluid rounded mb-2">
                        <img src="https://placehold.co/200x120/10B981/FFFFFF?text=SaaS+Features" alt="Blog 2" class="img-fluid rounded">
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Feature Icons</h5>
                    </div>
                    <div class="card-body">
                        <img src="assets/images/features/feature-1.svg" alt="Feature 1" width="64" height="64" class="me-3">
                        <img src="assets/images/features/feature-2.svg" alt="Feature 2" width="64" height="64" class="me-3">
                        <img src="assets/images/features/feature-3.svg" alt="Feature 3" width="64" height="64">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-5">
            <h3>Status</h3>
            <div class="alert alert-success">
                <strong>✅ Images Updated Successfully!</strong><br>
                All placeholder images have been replaced with working placeholder.com URLs that will display immediately.
                The SVG logo and feature icons are also properly created and should display correctly.
            </div>
            
            <div class="alert alert-info">
                <strong>📝 Next Steps:</strong><br>
                1. Replace placeholder.com URLs with your own branded images<br>
                2. Update the logo.svg with your company branding<br>
                3. Add real product screenshots and team photos<br>
                4. Optimize all images for web performance
            </div>
        </div>
    </div>
</body>
</html>
