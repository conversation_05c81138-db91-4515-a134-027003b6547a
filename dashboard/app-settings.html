<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Application Settings - SaaSShip</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Google Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    
    <!-- Custom Theme CSS -->
    <link href="../assets/css/themes/dashboard-default.css" rel="stylesheet">
    <link href="../assets/css/dashboard.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <div class="dashboard-wrapper">
        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-brand" href="#">
                <img src="../assets/images/logo.svg" alt="SaaSShip">
                <span class="sidebar-brand-text">SaaSShip</span>
            </div>
            
            <button class="sidebar-toggle" id="sidebarToggle">
                <span class="material-icons-outlined">chevron_left</span>
            </button>
            
            <div class="sidebar-nav">
                <div class="sidebar-nav-item">
                    <a href="index.html" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">dashboard</span>
                        <span class="sidebar-nav-text">Dashboard</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="products.html" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">inventory_2</span>
                        <span class="sidebar-nav-text">Products</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="#" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">people</span>
                        <span class="sidebar-nav-text">Users</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="#" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">analytics</span>
                        <span class="sidebar-nav-text">Analytics</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="#" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">payments</span>
                        <span class="sidebar-nav-text">Billing</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="app-settings.html" class="sidebar-nav-link active">
                        <span class="sidebar-nav-icon material-icons-outlined">settings</span>
                        <span class="sidebar-nav-text">App Settings</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="user-settings.html" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">account_circle</span>
                        <span class="sidebar-nav-text">User Settings</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="#" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">help</span>
                        <span class="sidebar-nav-text">Help & Support</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Navigation -->
            <header class="topnav">
                <div class="topnav-left">
                    <button class="btn btn-link d-lg-none" id="mobileSidebarToggle">
                        <span class="material-icons-outlined">menu</span>
                    </button>
                    <h1 class="topnav-title">Application Settings</h1>
                    <span class="topnav-breadcrumb">/ System Configuration</span>
                </div>
                
                <div class="topnav-right">
                    <!-- Notifications -->
                    <div class="notification-dropdown dropdown">
                        <button class="btn btn-link" data-bs-toggle="dropdown">
                            <span class="material-icons-outlined">notifications</span>
                            <span class="notification-badge">3</span>
                        </button>
                        <div class="dropdown-menu dropdown-menu-end">
                            <h6 class="dropdown-header">Notifications</h6>
                            <a class="dropdown-item" href="#">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <span class="material-icons-outlined text-primary">info</span>
                                    </div>
                                    <div class="flex-grow-1 ms-2">
                                        <h6 class="mb-1">New user registered</h6>
                                        <p class="mb-0 small text-muted">2 minutes ago</p>
                                    </div>
                                </div>
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item text-center" href="#">View all notifications</a>
                        </div>
                    </div>
                    
                    <!-- User Menu -->
                    <div class="dropdown">
                        <div class="user-dropdown" data-bs-toggle="dropdown">
                            <img src="../assets/images/avatars/user-1.jpg" alt="User" class="user-avatar">
                            <div class="user-info">
                                <span class="user-name">John Doe</span>
                                <span class="user-role">Administrator</span>
                            </div>
                            <span class="material-icons-outlined ms-2">expand_more</span>
                        </div>
                        <div class="dropdown-menu dropdown-menu-end">
                            <a class="dropdown-item" href="user-settings.html">
                                <span class="material-icons-outlined me-2">account_circle</span>
                                Profile
                            </a>
                            <a class="dropdown-item" href="user-settings.html">
                                <span class="material-icons-outlined me-2">settings</span>
                                Settings
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="../index.html">
                                <span class="material-icons-outlined me-2">logout</span>
                                Sign Out
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="content">
                <!-- Content Header -->
                <div class="content-header">
                    <h2 class="content-title">Application Settings</h2>
                    <p class="content-subtitle">Configure your application's global settings and preferences</p>
                </div>

                <!-- Settings Navigation -->
                <div class="row g-4">
                    <div class="col-lg-3">
                        <div class="card">
                            <div class="card-body p-0">
                                <div class="list-group list-group-flush">
                                    <a href="#general" class="list-group-item list-group-item-action active" data-bs-toggle="pill">
                                        <span class="material-icons-outlined me-2">settings</span>
                                        General
                                    </a>
                                    <a href="#appearance" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                                        <span class="material-icons-outlined me-2">palette</span>
                                        Appearance
                                    </a>
                                    <a href="#email" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                                        <span class="material-icons-outlined me-2">email</span>
                                        Email Settings
                                    </a>
                                    <a href="#sms" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                                        <span class="material-icons-outlined me-2">sms</span>
                                        SMS Settings
                                    </a>
                                    <a href="#payments" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                                        <span class="material-icons-outlined me-2">payment</span>
                                        Payment Settings
                                    </a>
                                    <a href="#security" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                                        <span class="material-icons-outlined me-2">security</span>
                                        Security
                                    </a>
                                    <a href="#integrations" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                                        <span class="material-icons-outlined me-2">extension</span>
                                        Integrations
                                    </a>
                                    <a href="#backup" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                                        <span class="material-icons-outlined me-2">backup</span>
                                        Backup & Recovery
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-9">
                        <div class="tab-content">
                            <!-- General Settings -->
                            <div class="tab-pane fade show active" id="general">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">General Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="generalSettingsForm">
                                            <div class="row g-3">
                                                <div class="col-md-6">
                                                    <label for="appName" class="form-label">Application Name</label>
                                                    <input type="text" class="form-control" id="appName" value="SaaSShip">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="appUrl" class="form-label">Application URL</label>
                                                    <input type="url" class="form-control" id="appUrl" value="https://saasship.com">
                                                </div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="appDescription" class="form-label">Application Description</label>
                                                <textarea class="form-control" id="appDescription" rows="3">Modern SaaS platform for building and scaling your business</textarea>
                                            </div>
                                            
                                            <div class="row g-3">
                                                <div class="col-md-6">
                                                    <label for="timezone" class="form-label">Default Timezone</label>
                                                    <select class="form-select" id="timezone">
                                                        <option value="UTC">UTC</option>
                                                        <option value="America/New_York" selected>Eastern Time</option>
                                                        <option value="America/Chicago">Central Time</option>
                                                        <option value="America/Denver">Mountain Time</option>
                                                        <option value="America/Los_Angeles">Pacific Time</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="dateFormat" class="form-label">Date Format</label>
                                                    <select class="form-select" id="dateFormat">
                                                        <option value="MM/DD/YYYY" selected>MM/DD/YYYY</option>
                                                        <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                                                        <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                                                    </select>
                                                </div>
                                            </div>
                                            
                                            <div class="row g-3">
                                                <div class="col-md-6">
                                                    <label for="currency" class="form-label">Default Currency</label>
                                                    <select class="form-select" id="currency">
                                                        <option value="USD" selected>USD - US Dollar</option>
                                                        <option value="EUR">EUR - Euro</option>
                                                        <option value="GBP">GBP - British Pound</option>
                                                        <option value="CAD">CAD - Canadian Dollar</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="language" class="form-label">Default Language</label>
                                                    <select class="form-select" id="language">
                                                        <option value="en" selected>English</option>
                                                        <option value="es">Spanish</option>
                                                        <option value="fr">French</option>
                                                        <option value="de">German</option>
                                                    </select>
                                                </div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="maintenanceMode">
                                                    <label class="form-check-label" for="maintenanceMode">
                                                        Enable Maintenance Mode
                                                    </label>
                                                    <div class="form-text">When enabled, only administrators can access the application</div>
                                                </div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="userRegistration" checked>
                                                    <label class="form-check-label" for="userRegistration">
                                                        Allow User Registration
                                                    </label>
                                                    <div class="form-text">Allow new users to register for accounts</div>
                                                </div>
                                            </div>
                                            
                                            <div class="d-flex justify-content-end">
                                                <button type="submit" class="btn btn-primary">Save Changes</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Appearance Settings -->
                            <div class="tab-pane fade" id="appearance">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Appearance Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="appearanceSettingsForm">
                                            <div class="mb-4">
                                                <label class="form-label">Theme</label>
                                                <div class="row g-3">
                                                    <div class="col-md-6">
                                                        <div class="card theme-card" data-theme="light">
                                                            <div class="card-body text-center">
                                                                <div class="theme-preview bg-light border rounded mb-2" style="height: 60px;"></div>
                                                                <h6 class="mb-1">Light Theme</h6>
                                                                <small class="text-muted">Clean and bright interface</small>
                                                                <div class="form-check mt-2">
                                                                    <input class="form-check-input" type="radio" name="theme" id="lightTheme" value="light" checked>
                                                                    <label class="form-check-label" for="lightTheme">Select</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="card theme-card" data-theme="dark">
                                                            <div class="card-body text-center">
                                                                <div class="theme-preview bg-dark border rounded mb-2" style="height: 60px;"></div>
                                                                <h6 class="mb-1">Dark Theme</h6>
                                                                <small class="text-muted">Easy on the eyes</small>
                                                                <div class="form-check mt-2">
                                                                    <input class="form-check-input" type="radio" name="theme" id="darkTheme" value="dark">
                                                                    <label class="form-check-label" for="darkTheme">Select</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="primaryColor" class="form-label">Primary Color</label>
                                                <div class="d-flex align-items-center">
                                                    <input type="color" class="form-control form-control-color me-3" id="primaryColor" value="#3b82f6">
                                                    <span class="text-muted">#3b82f6</span>
                                                </div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="logoUpload" class="form-label">Application Logo</label>
                                                <input type="file" class="form-control" id="logoUpload" accept="image/*">
                                                <div class="form-text">Upload a logo (PNG, JPG, SVG, max 2MB)</div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="faviconUpload" class="form-label">Favicon</label>
                                                <input type="file" class="form-control" id="faviconUpload" accept="image/*">
                                                <div class="form-text">Upload a favicon (ICO, PNG, 32x32px recommended)</div>
                                            </div>
                                            
                                            <div class="d-flex justify-content-end">
                                                <button type="submit" class="btn btn-primary">Save Changes</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Email Settings -->
                            <div class="tab-pane fade" id="email">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Email Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="emailSettingsForm">
                                            <div class="row g-3">
                                                <div class="col-md-6">
                                                    <label for="smtpHost" class="form-label">SMTP Host</label>
                                                    <input type="text" class="form-control" id="smtpHost" placeholder="smtp.gmail.com">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="smtpPort" class="form-label">SMTP Port</label>
                                                    <input type="number" class="form-control" id="smtpPort" value="587">
                                                </div>
                                            </div>
                                            
                                            <div class="row g-3">
                                                <div class="col-md-6">
                                                    <label for="smtpUsername" class="form-label">SMTP Username</label>
                                                    <input type="email" class="form-control" id="smtpUsername" placeholder="<EMAIL>">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="smtpPassword" class="form-label">SMTP Password</label>
                                                    <input type="password" class="form-control" id="smtpPassword">
                                                </div>
                                            </div>
                                            
                                            <div class="row g-3">
                                                <div class="col-md-6">
                                                    <label for="fromEmail" class="form-label">From Email</label>
                                                    <input type="email" class="form-control" id="fromEmail" placeholder="<EMAIL>">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="fromName" class="form-label">From Name</label>
                                                    <input type="text" class="form-control" id="fromName" value="SaaSShip">
                                                </div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="encryption" class="form-label">Encryption</label>
                                                <select class="form-select" id="encryption">
                                                    <option value="tls" selected>TLS</option>
                                                    <option value="ssl">SSL</option>
                                                    <option value="none">None</option>
                                                </select>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                                                    <label class="form-check-label" for="emailNotifications">
                                                        Enable Email Notifications
                                                    </label>
                                                </div>
                                            </div>
                                            
                                            <div class="d-flex justify-content-between">
                                                <button type="button" class="btn btn-outline-secondary" onclick="testEmailSettings()">
                                                    <span class="material-icons-outlined me-2">send</span>
                                                    Test Email
                                                </button>
                                                <button type="submit" class="btn btn-primary">Save Changes</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- SMS Settings -->
                            <div class="tab-pane fade" id="sms">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">SMS Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="smsSettingsForm">
                                            <div class="mb-3">
                                                <label for="smsProvider" class="form-label">SMS Provider</label>
                                                <select class="form-select" id="smsProvider">
                                                    <option value="">Select Provider</option>
                                                    <option value="twilio">Twilio</option>
                                                    <option value="nexmo">Vonage (Nexmo)</option>
                                                    <option value="aws">AWS SNS</option>
                                                </select>
                                            </div>
                                            
                                            <div class="row g-3">
                                                <div class="col-md-6">
                                                    <label for="smsApiKey" class="form-label">API Key</label>
                                                    <input type="text" class="form-control" id="smsApiKey">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="smsApiSecret" class="form-label">API Secret</label>
                                                    <input type="password" class="form-control" id="smsApiSecret">
                                                </div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="smsFromNumber" class="form-label">From Number</label>
                                                <input type="tel" class="form-control" id="smsFromNumber" placeholder="+1234567890">
                                            </div>
                                            
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="smsNotifications">
                                                    <label class="form-check-label" for="smsNotifications">
                                                        Enable SMS Notifications
                                                    </label>
                                                </div>
                                            </div>
                                            
                                            <div class="d-flex justify-content-between">
                                                <button type="button" class="btn btn-outline-secondary" onclick="testSmsSettings()">
                                                    <span class="material-icons-outlined me-2">sms</span>
                                                    Test SMS
                                                </button>
                                                <button type="submit" class="btn btn-primary">Save Changes</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Settings -->
                            <div class="tab-pane fade" id="payments">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Payment Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="paymentSettingsForm">
                                            <div class="mb-4">
                                                <label class="form-label">Payment Providers</label>
                                                <div class="row g-3">
                                                    <div class="col-md-6">
                                                        <div class="card">
                                                            <div class="card-body">
                                                                <div class="d-flex justify-content-between align-items-center">
                                                                    <div>
                                                                        <h6 class="mb-1">Stripe</h6>
                                                                        <small class="text-muted">Credit cards, digital wallets</small>
                                                                    </div>
                                                                    <div class="form-check form-switch">
                                                                        <input class="form-check-input" type="checkbox" id="stripeEnabled" checked>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="card">
                                                            <div class="card-body">
                                                                <div class="d-flex justify-content-between align-items-center">
                                                                    <div>
                                                                        <h6 class="mb-1">PayPal</h6>
                                                                        <small class="text-muted">PayPal payments</small>
                                                                    </div>
                                                                    <div class="form-check form-switch">
                                                                        <input class="form-check-input" type="checkbox" id="paypalEnabled">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="row g-3">
                                                <div class="col-md-6">
                                                    <label for="stripePublishableKey" class="form-label">Stripe Publishable Key</label>
                                                    <input type="text" class="form-control" id="stripePublishableKey" placeholder="pk_test_...">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="stripeSecretKey" class="form-label">Stripe Secret Key</label>
                                                    <input type="password" class="form-control" id="stripeSecretKey" placeholder="sk_test_...">
                                                </div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="webhookEndpoint" class="form-label">Webhook Endpoint</label>
                                                <input type="url" class="form-control" id="webhookEndpoint" value="https://saasship.com/webhooks/stripe" readonly>
                                                <div class="form-text">Configure this URL in your Stripe dashboard</div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="testMode" checked>
                                                    <label class="form-check-label" for="testMode">
                                                        Test Mode
                                                    </label>
                                                    <div class="form-text">Use test API keys for development</div>
                                                </div>
                                            </div>
                                            
                                            <div class="d-flex justify-content-end">
                                                <button type="submit" class="btn btn-primary">Save Changes</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Security Settings -->
                            <div class="tab-pane fade" id="security">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Security Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="securitySettingsForm">
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="twoFactorAuth" checked>
                                                    <label class="form-check-label" for="twoFactorAuth">
                                                        Require Two-Factor Authentication
                                                    </label>
                                                    <div class="form-text">Force all users to enable 2FA</div>
                                                </div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="sessionTimeout" class="form-label">Session Timeout (minutes)</label>
                                                <input type="number" class="form-control" id="sessionTimeout" value="60" min="5" max="1440">
                                                <div class="form-text">Automatically log out inactive users</div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="passwordPolicy" class="form-label">Password Policy</label>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="minLength" checked>
                                                    <label class="form-check-label" for="minLength">
                                                        Minimum 8 characters
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="requireUppercase" checked>
                                                    <label class="form-check-label" for="requireUppercase">
                                                        Require uppercase letters
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="requireNumbers" checked>
                                                    <label class="form-check-label" for="requireNumbers">
                                                        Require numbers
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="requireSpecialChars">
                                                    <label class="form-check-label" for="requireSpecialChars">
                                                        Require special characters
                                                    </label>
                                                </div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="loginAttempts" class="form-label">Max Login Attempts</label>
                                                <input type="number" class="form-control" id="loginAttempts" value="5" min="3" max="10">
                                                <div class="form-text">Lock account after failed attempts</div>
                                            </div>
                                            
                                            <div class="d-flex justify-content-end">
                                                <button type="submit" class="btn btn-primary">Save Changes</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Integrations -->
                            <div class="tab-pane fade" id="integrations">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Third-Party Integrations</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row g-4">
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                                            <div>
                                                                <h6 class="mb-1">Google Analytics</h6>
                                                                <small class="text-muted">Track website analytics</small>
                                                            </div>
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox" id="googleAnalytics">
                                                            </div>
                                                        </div>
                                                        <input type="text" class="form-control" placeholder="GA Tracking ID" id="gaTrackingId">
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                                            <div>
                                                                <h6 class="mb-1">Slack</h6>
                                                                <small class="text-muted">Send notifications to Slack</small>
                                                            </div>
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox" id="slackIntegration">
                                                            </div>
                                                        </div>
                                                        <input type="url" class="form-control" placeholder="Webhook URL" id="slackWebhook">
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                                            <div>
                                                                <h6 class="mb-1">Mailchimp</h6>
                                                                <small class="text-muted">Email marketing integration</small>
                                                            </div>
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox" id="mailchimpIntegration">
                                                            </div>
                                                        </div>
                                                        <input type="text" class="form-control" placeholder="API Key" id="mailchimpApiKey">
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                                            <div>
                                                                <h6 class="mb-1">Zapier</h6>
                                                                <small class="text-muted">Workflow automation</small>
                                                            </div>
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox" id="zapierIntegration">
                                                            </div>
                                                        </div>
                                                        <input type="text" class="form-control" placeholder="API Key" id="zapierApiKey">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="d-flex justify-content-end mt-4">
                                            <button type="button" class="btn btn-primary" onclick="saveIntegrations()">Save Changes</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Backup & Recovery -->
                            <div class="tab-pane fade" id="backup">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Backup & Recovery</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-4">
                                            <h6>Automatic Backups</h6>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="autoBackup" checked>
                                                <label class="form-check-label" for="autoBackup">
                                                    Enable automatic backups
                                                </label>
                                            </div>
                                            
                                            <div class="row g-3 mt-2">
                                                <div class="col-md-6">
                                                    <label for="backupFrequency" class="form-label">Backup Frequency</label>
                                                    <select class="form-select" id="backupFrequency">
                                                        <option value="daily" selected>Daily</option>
                                                        <option value="weekly">Weekly</option>
                                                        <option value="monthly">Monthly</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="backupRetention" class="form-label">Retention Period (days)</label>
                                                    <input type="number" class="form-control" id="backupRetention" value="30" min="1" max="365">
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-4">
                                            <h6>Recent Backups</h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th>Date</th>
                                                            <th>Size</th>
                                                            <th>Status</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td>Dec 15, 2024 02:00 AM</td>
                                                            <td>245 MB</td>
                                                            <td><span class="badge bg-success">Completed</span></td>
                                                            <td>
                                                                <button class="btn btn-sm btn-outline-primary">Download</button>
                                                                <button class="btn btn-sm btn-outline-secondary">Restore</button>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>Dec 14, 2024 02:00 AM</td>
                                                            <td>243 MB</td>
                                                            <td><span class="badge bg-success">Completed</span></td>
                                                            <td>
                                                                <button class="btn btn-sm btn-outline-primary">Download</button>
                                                                <button class="btn btn-sm btn-outline-secondary">Restore</button>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>Dec 13, 2024 02:00 AM</td>
                                                            <td>241 MB</td>
                                                            <td><span class="badge bg-success">Completed</span></td>
                                                            <td>
                                                                <button class="btn btn-sm btn-outline-primary">Download</button>
                                                                <button class="btn btn-sm btn-outline-secondary">Restore</button>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        
                                        <div class="d-flex justify-content-between">
                                            <button type="button" class="btn btn-outline-primary" onclick="createBackup()">
                                                <span class="material-icons-outlined me-2">backup</span>
                                                Create Backup Now
                                            </button>
                                            <button type="button" class="btn btn-primary" onclick="saveBackupSettings()">Save Settings</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/dashboard.js"></script>
    <script src="../assets/js/app-settings.js"></script>
</body>
</html>
