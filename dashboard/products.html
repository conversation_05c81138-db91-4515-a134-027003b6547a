<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - SaaSShip</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Google Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    
    <!-- Custom Theme CSS -->
    <link href="../assets/css/themes/dashboard-default.css" rel="stylesheet">
    <link href="../assets/css/dashboard.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <div class="dashboard-wrapper">
        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-brand" href="#">
                <img src="../assets/images/logo.svg" alt="SaaSShip">
                <span class="sidebar-brand-text">SaaSShip</span>
            </div>
            
            <button class="sidebar-toggle" id="sidebarToggle">
                <span class="material-icons-outlined">chevron_left</span>
            </button>
            
            <div class="sidebar-nav">
                <div class="sidebar-nav-item">
                    <a href="index.html" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">dashboard</span>
                        <span class="sidebar-nav-text">Dashboard</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="products.html" class="sidebar-nav-link active">
                        <span class="sidebar-nav-icon material-icons-outlined">inventory_2</span>
                        <span class="sidebar-nav-text">Products</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="#" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">people</span>
                        <span class="sidebar-nav-text">Users</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="#" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">analytics</span>
                        <span class="sidebar-nav-text">Analytics</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="#" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">payments</span>
                        <span class="sidebar-nav-text">Billing</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="app-settings.html" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">settings</span>
                        <span class="sidebar-nav-text">App Settings</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="user-settings.html" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">account_circle</span>
                        <span class="sidebar-nav-text">User Settings</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="#" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">help</span>
                        <span class="sidebar-nav-text">Help & Support</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Navigation -->
            <header class="topnav">
                <div class="topnav-left">
                    <button class="btn btn-link d-lg-none" id="mobileSidebarToggle">
                        <span class="material-icons-outlined">menu</span>
                    </button>
                    <h1 class="topnav-title">Products</h1>
                    <span class="topnav-breadcrumb">/ Manage Products</span>
                </div>
                
                <div class="topnav-right">
                    <!-- Search -->
                    <div class="topnav-search d-none d-lg-block">
                        <span class="material-icons-outlined search-icon">search</span>
                        <input type="text" class="form-control" placeholder="Search products..." id="productSearch">
                    </div>
                    
                    <!-- Notifications -->
                    <div class="notification-dropdown dropdown">
                        <button class="btn btn-link" data-bs-toggle="dropdown">
                            <span class="material-icons-outlined">notifications</span>
                            <span class="notification-badge">3</span>
                        </button>
                        <div class="dropdown-menu dropdown-menu-end">
                            <h6 class="dropdown-header">Notifications</h6>
                            <a class="dropdown-item" href="#">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <span class="material-icons-outlined text-primary">info</span>
                                    </div>
                                    <div class="flex-grow-1 ms-2">
                                        <h6 class="mb-1">New user registered</h6>
                                        <p class="mb-0 small text-muted">2 minutes ago</p>
                                    </div>
                                </div>
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item text-center" href="#">View all notifications</a>
                        </div>
                    </div>
                    
                    <!-- User Menu -->
                    <div class="dropdown">
                        <div class="user-dropdown" data-bs-toggle="dropdown">
                            <img src="../assets/images/avatars/user-1.jpg" alt="User" class="user-avatar">
                            <div class="user-info">
                                <span class="user-name">John Doe</span>
                                <span class="user-role">Administrator</span>
                            </div>
                            <span class="material-icons-outlined ms-2">expand_more</span>
                        </div>
                        <div class="dropdown-menu dropdown-menu-end">
                            <a class="dropdown-item" href="user-settings.html">
                                <span class="material-icons-outlined me-2">account_circle</span>
                                Profile
                            </a>
                            <a class="dropdown-item" href="user-settings.html">
                                <span class="material-icons-outlined me-2">settings</span>
                                Settings
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="../index.html">
                                <span class="material-icons-outlined me-2">logout</span>
                                Sign Out
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="content">
                <!-- Content Header -->
                <div class="content-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="content-title">Products</h2>
                            <p class="content-subtitle">Manage your product catalog and inventory</p>
                        </div>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#productModal" onclick="openProductModal()">
                            <span class="material-icons-outlined me-2">add</span>
                            Add Product
                        </button>
                    </div>
                </div>

                <!-- Filters and Actions -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row g-3 align-items-center">
                            <div class="col-md-3">
                                <select class="form-select" id="categoryFilter">
                                    <option value="">All Categories</option>
                                    <option value="software">Software</option>
                                    <option value="service">Service</option>
                                    <option value="subscription">Subscription</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="statusFilter">
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="draft">Draft</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="sortBy">
                                    <option value="name">Sort by Name</option>
                                    <option value="price">Sort by Price</option>
                                    <option value="created">Sort by Date</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <div class="btn-group w-100">
                                    <button class="btn btn-outline-secondary" onclick="exportProducts()">
                                        <span class="material-icons-outlined me-2">file_download</span>
                                        Export
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="refreshProducts()">
                                        <span class="material-icons-outlined">refresh</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Products Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Product List</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="productsTable">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" class="form-check-input" id="selectAll">
                                        </th>
                                        <th>Product</th>
                                        <th>Category</th>
                                        <th>Price</th>
                                        <th>Status</th>
                                        <th>Stock</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="productsTableBody">
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="form-check-input product-checkbox" data-id="1">
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="../assets/images/products/product-1.jpg" alt="Product" class="rounded me-3" width="40" height="40">
                                                <div>
                                                    <h6 class="mb-0">Starter Plan</h6>
                                                    <small class="text-muted">Perfect for small teams</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="badge bg-primary">Subscription</span></td>
                                        <td>$29.00/month</td>
                                        <td><span class="badge bg-success">Active</span></td>
                                        <td>Unlimited</td>
                                        <td>Dec 1, 2024</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="editProduct(1)" title="Edit">
                                                    <span class="material-icons-outlined">edit</span>
                                                </button>
                                                <button class="btn btn-outline-secondary" onclick="viewProduct(1)" title="View">
                                                    <span class="material-icons-outlined">visibility</span>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteProduct(1)" title="Delete">
                                                    <span class="material-icons-outlined">delete</span>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="form-check-input product-checkbox" data-id="2">
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="../assets/images/products/product-2.jpg" alt="Product" class="rounded me-3" width="40" height="40">
                                                <div>
                                                    <h6 class="mb-0">Professional Plan</h6>
                                                    <small class="text-muted">Best for growing businesses</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="badge bg-primary">Subscription</span></td>
                                        <td>$79.00/month</td>
                                        <td><span class="badge bg-success">Active</span></td>
                                        <td>Unlimited</td>
                                        <td>Dec 1, 2024</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="editProduct(2)" title="Edit">
                                                    <span class="material-icons-outlined">edit</span>
                                                </button>
                                                <button class="btn btn-outline-secondary" onclick="viewProduct(2)" title="View">
                                                    <span class="material-icons-outlined">visibility</span>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteProduct(2)" title="Delete">
                                                    <span class="material-icons-outlined">delete</span>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="form-check-input product-checkbox" data-id="3">
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="../assets/images/products/product-3.jpg" alt="Product" class="rounded me-3" width="40" height="40">
                                                <div>
                                                    <h6 class="mb-0">Enterprise Plan</h6>
                                                    <small class="text-muted">For large organizations</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="badge bg-primary">Subscription</span></td>
                                        <td>$199.00/month</td>
                                        <td><span class="badge bg-success">Active</span></td>
                                        <td>Unlimited</td>
                                        <td>Dec 1, 2024</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="editProduct(3)" title="Edit">
                                                    <span class="material-icons-outlined">edit</span>
                                                </button>
                                                <button class="btn btn-outline-secondary" onclick="viewProduct(3)" title="View">
                                                    <span class="material-icons-outlined">visibility</span>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteProduct(3)" title="Delete">
                                                    <span class="material-icons-outlined">delete</span>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="form-check-input product-checkbox" data-id="4">
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="../assets/images/products/product-4.jpg" alt="Product" class="rounded me-3" width="40" height="40">
                                                <div>
                                                    <h6 class="mb-0">API Access</h6>
                                                    <small class="text-muted">Developer API integration</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="badge bg-info">Service</span></td>
                                        <td>$49.00/month</td>
                                        <td><span class="badge bg-warning">Draft</span></td>
                                        <td>Unlimited</td>
                                        <td>Nov 28, 2024</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="editProduct(4)" title="Edit">
                                                    <span class="material-icons-outlined">edit</span>
                                                </button>
                                                <button class="btn btn-outline-secondary" onclick="viewProduct(4)" title="View">
                                                    <span class="material-icons-outlined">visibility</span>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteProduct(4)" title="Delete">
                                                    <span class="material-icons-outlined">delete</span>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="form-check-input product-checkbox" data-id="5">
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="../assets/images/products/product-5.jpg" alt="Product" class="rounded me-3" width="40" height="40">
                                                <div>
                                                    <h6 class="mb-0">Custom Integration</h6>
                                                    <small class="text-muted">One-time setup service</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="badge bg-success">Service</span></td>
                                        <td>$299.00</td>
                                        <td><span class="badge bg-secondary">Inactive</span></td>
                                        <td>50</td>
                                        <td>Nov 25, 2024</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="editProduct(5)" title="Edit">
                                                    <span class="material-icons-outlined">edit</span>
                                                </button>
                                                <button class="btn btn-outline-secondary" onclick="viewProduct(5)" title="View">
                                                    <span class="material-icons-outlined">visibility</span>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteProduct(5)" title="Delete">
                                                    <span class="material-icons-outlined">delete</span>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted">
                                Showing 1 to 5 of 5 products
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm mb-0">
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#">Previous</a>
                                    </li>
                                    <li class="page-item active">
                                        <a class="page-link" href="#">1</a>
                                    </li>
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#">Next</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Product Modal -->
    <div class="modal fade" id="productModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productModalTitle">Add New Product</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="productForm">
                        <input type="hidden" id="productId">
                        
                        <div class="row g-3">
                            <div class="col-md-8">
                                <label for="productName" class="form-label">Product Name *</label>
                                <input type="text" class="form-control" id="productName" required>
                            </div>
                            <div class="col-md-4">
                                <label for="productCategory" class="form-label">Category *</label>
                                <select class="form-select" id="productCategory" required>
                                    <option value="">Select Category</option>
                                    <option value="software">Software</option>
                                    <option value="service">Service</option>
                                    <option value="subscription">Subscription</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="productDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="productDescription" rows="3"></textarea>
                        </div>
                        
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="productPrice" class="form-label">Price *</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="productPrice" step="0.01" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label for="productStock" class="form-label">Stock</label>
                                <input type="number" class="form-control" id="productStock" placeholder="Leave empty for unlimited">
                            </div>
                            <div class="col-md-4">
                                <label for="productStatus" class="form-label">Status *</label>
                                <select class="form-select" id="productStatus" required>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="draft">Draft</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="productImage" class="form-label">Product Image</label>
                            <input type="file" class="form-control" id="productImage" accept="image/*">
                            <div class="form-text">Upload a product image (JPG, PNG, max 2MB)</div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="productFeatured">
                                <label class="form-check-label" for="productFeatured">
                                    Featured Product
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveProduct()">Save Product</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/dashboard.js"></script>
    <script src="../assets/js/products.js"></script>
</body>
</html>
