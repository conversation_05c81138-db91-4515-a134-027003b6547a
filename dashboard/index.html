<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - SaaSShip</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Google Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    
    <!-- Custom Theme CSS -->
    <link href="../assets/css/themes/dashboard-default.css" rel="stylesheet">
    <link href="../assets/css/dashboard.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <div class="dashboard-wrapper">
        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-brand" href="#">
                <img src="../assets/images/logo.svg" alt="SaaSShip">
                <span class="sidebar-brand-text">SaaSShip</span>
            </div>
            
            <button class="sidebar-toggle" id="sidebarToggle">
                <span class="material-icons-outlined">chevron_left</span>
            </button>
            
            <div class="sidebar-nav">
                <div class="sidebar-nav-item">
                    <a href="index.html" class="sidebar-nav-link active">
                        <span class="sidebar-nav-icon material-icons-outlined">dashboard</span>
                        <span class="sidebar-nav-text">Dashboard</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="products.html" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">inventory_2</span>
                        <span class="sidebar-nav-text">Products</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="#" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">people</span>
                        <span class="sidebar-nav-text">Users</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="#" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">analytics</span>
                        <span class="sidebar-nav-text">Analytics</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="#" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">payments</span>
                        <span class="sidebar-nav-text">Billing</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="app-settings.html" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">settings</span>
                        <span class="sidebar-nav-text">App Settings</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="user-settings.html" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">account_circle</span>
                        <span class="sidebar-nav-text">User Settings</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="#" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">help</span>
                        <span class="sidebar-nav-text">Help & Support</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Navigation -->
            <header class="topnav">
                <div class="topnav-left">
                    <button class="btn btn-link d-lg-none" id="mobileSidebarToggle">
                        <span class="material-icons-outlined">menu</span>
                    </button>
                    <h1 class="topnav-title">Dashboard</h1>
                    <span class="topnav-breadcrumb">/ Overview</span>
                </div>
                
                <div class="topnav-right">
                    <!-- Search -->
                    <div class="topnav-search d-none d-lg-block">
                        <span class="material-icons-outlined search-icon">search</span>
                        <input type="text" class="form-control" placeholder="Search...">
                    </div>
                    
                    <!-- Notifications -->
                    <div class="notification-dropdown dropdown">
                        <button class="btn btn-link" data-bs-toggle="dropdown">
                            <span class="material-icons-outlined">notifications</span>
                            <span class="notification-badge">3</span>
                        </button>
                        <div class="dropdown-menu dropdown-menu-end">
                            <h6 class="dropdown-header">Notifications</h6>
                            <a class="dropdown-item" href="#">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <span class="material-icons-outlined text-primary">info</span>
                                    </div>
                                    <div class="flex-grow-1 ms-2">
                                        <h6 class="mb-1">New user registered</h6>
                                        <p class="mb-0 small text-muted">2 minutes ago</p>
                                    </div>
                                </div>
                            </a>
                            <a class="dropdown-item" href="#">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <span class="material-icons-outlined text-success">check_circle</span>
                                    </div>
                                    <div class="flex-grow-1 ms-2">
                                        <h6 class="mb-1">Payment received</h6>
                                        <p class="mb-0 small text-muted">1 hour ago</p>
                                    </div>
                                </div>
                            </a>
                            <a class="dropdown-item" href="#">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <span class="material-icons-outlined text-warning">warning</span>
                                    </div>
                                    <div class="flex-grow-1 ms-2">
                                        <h6 class="mb-1">Server maintenance</h6>
                                        <p class="mb-0 small text-muted">3 hours ago</p>
                                    </div>
                                </div>
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item text-center" href="#">View all notifications</a>
                        </div>
                    </div>
                    
                    <!-- User Menu -->
                    <div class="dropdown">
                        <div class="user-dropdown" data-bs-toggle="dropdown">
                            <img src="../assets/images/avatars/user-1.jpg" alt="User" class="user-avatar">
                            <div class="user-info">
                                <span class="user-name">John Doe</span>
                                <span class="user-role">Administrator</span>
                            </div>
                            <span class="material-icons-outlined ms-2">expand_more</span>
                        </div>
                        <div class="dropdown-menu dropdown-menu-end">
                            <a class="dropdown-item" href="user-settings.html">
                                <span class="material-icons-outlined me-2">account_circle</span>
                                Profile
                            </a>
                            <a class="dropdown-item" href="user-settings.html">
                                <span class="material-icons-outlined me-2">settings</span>
                                Settings
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="../index.html">
                                <span class="material-icons-outlined me-2">logout</span>
                                Sign Out
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="content">
                <!-- Welcome Section -->
                <div class="content-header">
                    <h2 class="content-title">Welcome back, John!</h2>
                    <p class="content-subtitle">Here's what's happening with your business today.</p>
                </div>

                <!-- Stats Cards -->
                <div class="row g-4 mb-4">
                    <div class="col-xl-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-card-icon">
                                <span class="material-icons-outlined">people</span>
                            </div>
                            <div class="stats-card-value">2,847</div>
                            <div class="stats-card-label">Total Users</div>
                            <div class="stats-card-change positive">
                                <span class="material-icons-outlined">trending_up</span>
                                +12.5% from last month
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-card-icon">
                                <span class="material-icons-outlined">attach_money</span>
                            </div>
                            <div class="stats-card-value">$24,567</div>
                            <div class="stats-card-label">Monthly Revenue</div>
                            <div class="stats-card-change positive">
                                <span class="material-icons-outlined">trending_up</span>
                                +8.2% from last month
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-card-icon">
                                <span class="material-icons-outlined">shopping_cart</span>
                            </div>
                            <div class="stats-card-value">1,234</div>
                            <div class="stats-card-label">Orders</div>
                            <div class="stats-card-change negative">
                                <span class="material-icons-outlined">trending_down</span>
                                -3.1% from last month
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-card-icon">
                                <span class="material-icons-outlined">percent</span>
                            </div>
                            <div class="stats-card-value">94.2%</div>
                            <div class="stats-card-label">Conversion Rate</div>
                            <div class="stats-card-change positive">
                                <span class="material-icons-outlined">trending_up</span>
                                +2.4% from last month
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts and Tables Row -->
                <div class="row g-4 mb-4">
                    <!-- Revenue Chart -->
                    <div class="col-xl-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">Revenue Overview</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container" style="height: 300px; display: flex; align-items: center; justify-content: center; background: #f8f9fa; border-radius: 8px;">
                                    <div class="text-center">
                                        <span class="material-icons-outlined" style="font-size: 48px; color: #6c757d;">bar_chart</span>
                                        <p class="text-muted mt-2">Revenue chart would be displayed here</p>
                                        <small class="text-muted">Integration with Chart.js or similar library</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="col-xl-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">Quick Actions</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary">
                                        <span class="material-icons-outlined me-2">add</span>
                                        Add New Product
                                    </button>
                                    <button class="btn btn-outline-primary">
                                        <span class="material-icons-outlined me-2">person_add</span>
                                        Invite User
                                    </button>
                                    <button class="btn btn-outline-secondary">
                                        <span class="material-icons-outlined me-2">file_download</span>
                                        Export Data
                                    </button>
                                    <button class="btn btn-outline-secondary">
                                        <span class="material-icons-outlined me-2">settings</span>
                                        System Settings
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="row g-4">
                    <div class="col-xl-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">Recent Orders</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Order ID</th>
                                                <th>Customer</th>
                                                <th>Product</th>
                                                <th>Amount</th>
                                                <th>Status</th>
                                                <th>Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>#ORD-001</td>
                                                <td>Alice Johnson</td>
                                                <td>Professional Plan</td>
                                                <td>$79.00</td>
                                                <td><span class="badge bg-success">Completed</span></td>
                                                <td>Dec 15, 2024</td>
                                            </tr>
                                            <tr>
                                                <td>#ORD-002</td>
                                                <td>Bob Smith</td>
                                                <td>Starter Plan</td>
                                                <td>$29.00</td>
                                                <td><span class="badge bg-warning">Pending</span></td>
                                                <td>Dec 15, 2024</td>
                                            </tr>
                                            <tr>
                                                <td>#ORD-003</td>
                                                <td>Carol Davis</td>
                                                <td>Enterprise Plan</td>
                                                <td>$199.00</td>
                                                <td><span class="badge bg-success">Completed</span></td>
                                                <td>Dec 14, 2024</td>
                                            </tr>
                                            <tr>
                                                <td>#ORD-004</td>
                                                <td>David Wilson</td>
                                                <td>Professional Plan</td>
                                                <td>$79.00</td>
                                                <td><span class="badge bg-danger">Failed</span></td>
                                                <td>Dec 14, 2024</td>
                                            </tr>
                                            <tr>
                                                <td>#ORD-005</td>
                                                <td>Eva Brown</td>
                                                <td>Starter Plan</td>
                                                <td>$29.00</td>
                                                <td><span class="badge bg-success">Completed</span></td>
                                                <td>Dec 13, 2024</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">System Status</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>Server Status</span>
                                        <span class="status-indicator status-online"></span>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>Database</span>
                                        <span class="status-indicator status-online"></span>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>API Services</span>
                                        <span class="status-indicator status-online"></span>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>Payment Gateway</span>
                                        <span class="status-indicator status-away"></span>
                                    </div>
                                </div>
                                
                                <hr>
                                
                                <div class="mb-3">
                                    <h6 class="fw-semibold mb-2">Storage Usage</h6>
                                    <div class="progress mb-2">
                                        <div class="progress-bar bg-primary" style="width: 65%"></div>
                                    </div>
                                    <small class="text-muted">6.5 GB of 10 GB used</small>
                                </div>
                                
                                <div>
                                    <h6 class="fw-semibold mb-2">Bandwidth Usage</h6>
                                    <div class="progress mb-2">
                                        <div class="progress-bar bg-success" style="width: 45%"></div>
                                    </div>
                                    <small class="text-muted">450 GB of 1 TB used</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/dashboard.js"></script>
</body>
</html>
