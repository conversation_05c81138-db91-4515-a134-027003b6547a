<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Settings - SaaSShip</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Google Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    
    <!-- Custom Theme CSS -->
    <link href="../assets/css/themes/dashboard-default.css" rel="stylesheet">
    <link href="../assets/css/dashboard.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <div class="dashboard-wrapper">
        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-brand" href="#">
                <img src="../assets/images/logo.svg" alt="SaaSShip">
                <span class="sidebar-brand-text">SaaSShip</span>
            </div>
            
            <button class="sidebar-toggle" id="sidebarToggle">
                <span class="material-icons-outlined">chevron_left</span>
            </button>
            
            <div class="sidebar-nav">
                <div class="sidebar-nav-item">
                    <a href="index.html" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">dashboard</span>
                        <span class="sidebar-nav-text">Dashboard</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="products.html" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">inventory_2</span>
                        <span class="sidebar-nav-text">Products</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="#" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">people</span>
                        <span class="sidebar-nav-text">Users</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="#" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">analytics</span>
                        <span class="sidebar-nav-text">Analytics</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="#" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">payments</span>
                        <span class="sidebar-nav-text">Billing</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="app-settings.html" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">settings</span>
                        <span class="sidebar-nav-text">App Settings</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="user-settings.html" class="sidebar-nav-link active">
                        <span class="sidebar-nav-icon material-icons-outlined">account_circle</span>
                        <span class="sidebar-nav-text">User Settings</span>
                    </a>
                </div>
                
                <div class="sidebar-nav-item">
                    <a href="#" class="sidebar-nav-link">
                        <span class="sidebar-nav-icon material-icons-outlined">help</span>
                        <span class="sidebar-nav-text">Help & Support</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Navigation -->
            <header class="topnav">
                <div class="topnav-left">
                    <button class="btn btn-link d-lg-none" id="mobileSidebarToggle">
                        <span class="material-icons-outlined">menu</span>
                    </button>
                    <h1 class="topnav-title">User Settings</h1>
                    <span class="topnav-breadcrumb">/ Personal Preferences</span>
                </div>
                
                <div class="topnav-right">
                    <!-- Notifications -->
                    <div class="notification-dropdown dropdown">
                        <button class="btn btn-link" data-bs-toggle="dropdown">
                            <span class="material-icons-outlined">notifications</span>
                            <span class="notification-badge">3</span>
                        </button>
                        <div class="dropdown-menu dropdown-menu-end">
                            <h6 class="dropdown-header">Notifications</h6>
                            <a class="dropdown-item" href="#">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <span class="material-icons-outlined text-primary">info</span>
                                    </div>
                                    <div class="flex-grow-1 ms-2">
                                        <h6 class="mb-1">New user registered</h6>
                                        <p class="mb-0 small text-muted">2 minutes ago</p>
                                    </div>
                                </div>
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item text-center" href="#">View all notifications</a>
                        </div>
                    </div>
                    
                    <!-- User Menu -->
                    <div class="dropdown">
                        <div class="user-dropdown" data-bs-toggle="dropdown">
                            <img src="https://placehold.co/40x40/3B82F6/FFFFFF?text=JD" alt="User" class="user-avatar">
                            <div class="user-info">
                                <span class="user-name">John Doe</span>
                                <span class="user-role">Administrator</span>
                            </div>
                            <span class="material-icons-outlined ms-2">expand_more</span>
                        </div>
                        <div class="dropdown-menu dropdown-menu-end">
                            <a class="dropdown-item" href="user-settings.html">
                                <span class="material-icons-outlined me-2">account_circle</span>
                                Profile
                            </a>
                            <a class="dropdown-item" href="user-settings.html">
                                <span class="material-icons-outlined me-2">settings</span>
                                Settings
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="../index.html">
                                <span class="material-icons-outlined me-2">logout</span>
                                Sign Out
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="content">
                <!-- Content Header -->
                <div class="content-header">
                    <h2 class="content-title">User Settings</h2>
                    <p class="content-subtitle">Manage your personal account settings and preferences</p>
                </div>

                <!-- Settings Navigation -->
                <div class="row g-4">
                    <div class="col-lg-3">
                        <div class="card">
                            <div class="card-body p-0">
                                <div class="list-group list-group-flush">
                                    <a href="#profile" class="list-group-item list-group-item-action active" data-bs-toggle="pill">
                                        <span class="material-icons-outlined me-2">person</span>
                                        Profile Information
                                    </a>
                                    <a href="#password" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                                        <span class="material-icons-outlined me-2">lock</span>
                                        Password & Security
                                    </a>
                                    <a href="#avatar" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                                        <span class="material-icons-outlined me-2">photo_camera</span>
                                        Avatar & Photo
                                    </a>
                                    <a href="#subscription" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                                        <span class="material-icons-outlined me-2">card_membership</span>
                                        Subscription
                                    </a>
                                    <a href="#notifications" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                                        <span class="material-icons-outlined me-2">notifications</span>
                                        Notifications
                                    </a>
                                    <a href="#privacy" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                                        <span class="material-icons-outlined me-2">privacy_tip</span>
                                        Privacy & Data
                                    </a>
                                    <a href="#danger" class="list-group-item list-group-item-action text-danger" data-bs-toggle="pill">
                                        <span class="material-icons-outlined me-2">warning</span>
                                        Danger Zone
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-9">
                        <div class="tab-content">
                            <!-- Profile Information -->
                            <div class="tab-pane fade show active" id="profile">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Profile Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="profileForm">
                                            <div class="row g-3">
                                                <div class="col-md-6">
                                                    <label for="firstName" class="form-label">First Name</label>
                                                    <input type="text" class="form-control" id="firstName" value="John" required>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="lastName" class="form-label">Last Name</label>
                                                    <input type="text" class="form-control" id="lastName" value="Doe" required>
                                                </div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="email" class="form-label">Email Address</label>
                                                <input type="email" class="form-control" id="email" value="<EMAIL>" required>
                                                <div class="form-text">This email is used for login and notifications</div>
                                            </div>
                                            
                                            <div class="row g-3">
                                                <div class="col-md-6">
                                                    <label for="phone" class="form-label">Phone Number</label>
                                                    <input type="tel" class="form-control" id="phone" value="+****************">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="timezone" class="form-label">Timezone</label>
                                                    <select class="form-select" id="timezone">
                                                        <option value="America/New_York" selected>Eastern Time</option>
                                                        <option value="America/Chicago">Central Time</option>
                                                        <option value="America/Denver">Mountain Time</option>
                                                        <option value="America/Los_Angeles">Pacific Time</option>
                                                        <option value="UTC">UTC</option>
                                                    </select>
                                                </div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="bio" class="form-label">Bio</label>
                                                <textarea class="form-control" id="bio" rows="3" placeholder="Tell us about yourself...">Experienced product manager with a passion for building great user experiences.</textarea>
                                            </div>
                                            
                                            <div class="row g-3">
                                                <div class="col-md-6">
                                                    <label for="company" class="form-label">Company</label>
                                                    <input type="text" class="form-control" id="company" value="Acme Corp">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="jobTitle" class="form-label">Job Title</label>
                                                    <input type="text" class="form-control" id="jobTitle" value="Product Manager">
                                                </div>
                                            </div>
                                            
                                            <div class="d-flex justify-content-end">
                                                <button type="submit" class="btn btn-primary">Save Changes</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Password & Security -->
                            <div class="tab-pane fade" id="password">
                                <div class="row g-4">
                                    <!-- Change Password -->
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5 class="card-title mb-0">Change Password</h5>
                                            </div>
                                            <div class="card-body">
                                                <form id="passwordForm">
                                                    <div class="mb-3">
                                                        <label for="currentPassword" class="form-label">Current Password</label>
                                                        <input type="password" class="form-control" id="currentPassword" required>
                                                    </div>
                                                    
                                                    <div class="mb-3">
                                                        <label for="newPassword" class="form-label">New Password</label>
                                                        <input type="password" class="form-control" id="newPassword" required>
                                                        <div class="form-text">Password must be at least 8 characters long</div>
                                                    </div>
                                                    
                                                    <div class="mb-3">
                                                        <label for="confirmPassword" class="form-label">Confirm New Password</label>
                                                        <input type="password" class="form-control" id="confirmPassword" required>
                                                    </div>
                                                    
                                                    <div class="d-flex justify-content-end">
                                                        <button type="submit" class="btn btn-primary">Update Password</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Two-Factor Authentication -->
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5 class="card-title mb-0">Two-Factor Authentication</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-center mb-3">
                                                    <div>
                                                        <h6 class="mb-1">Authenticator App</h6>
                                                        <p class="text-muted mb-0">Use an authenticator app to generate verification codes</p>
                                                    </div>
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" id="twoFactorAuth" checked>
                                                        <label class="form-check-label" for="twoFactorAuth">Enabled</label>
                                                    </div>
                                                </div>
                                                
                                                <div class="alert alert-success" role="alert">
                                                    <span class="material-icons-outlined me-2">check_circle</span>
                                                    Two-factor authentication is currently enabled for your account.
                                                </div>
                                                
                                                <div class="d-flex gap-2">
                                                    <button type="button" class="btn btn-outline-secondary" onclick="regenerateBackupCodes()">
                                                        <span class="material-icons-outlined me-2">refresh</span>
                                                        Regenerate Backup Codes
                                                    </button>
                                                    <button type="button" class="btn btn-outline-danger" onclick="disable2FA()">
                                                        <span class="material-icons-outlined me-2">security</span>
                                                        Disable 2FA
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Active Sessions -->
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5 class="card-title mb-0">Active Sessions</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="session-item d-flex justify-content-between align-items-center py-3 border-bottom">
                                                    <div class="d-flex align-items-center">
                                                        <span class="material-icons-outlined me-3 text-success">computer</span>
                                                        <div>
                                                            <h6 class="mb-1">Chrome on macOS</h6>
                                                            <small class="text-muted">Current session • New York, NY</small>
                                                        </div>
                                                    </div>
                                                    <span class="badge bg-success">Current</span>
                                                </div>
                                                
                                                <div class="session-item d-flex justify-content-between align-items-center py-3 border-bottom">
                                                    <div class="d-flex align-items-center">
                                                        <span class="material-icons-outlined me-3 text-muted">phone_iphone</span>
                                                        <div>
                                                            <h6 class="mb-1">Safari on iPhone</h6>
                                                            <small class="text-muted">2 hours ago • New York, NY</small>
                                                        </div>
                                                    </div>
                                                    <button class="btn btn-sm btn-outline-danger" onclick="revokeSession('mobile')">Revoke</button>
                                                </div>
                                                
                                                <div class="session-item d-flex justify-content-between align-items-center py-3">
                                                    <div class="d-flex align-items-center">
                                                        <span class="material-icons-outlined me-3 text-muted">tablet</span>
                                                        <div>
                                                            <h6 class="mb-1">Chrome on iPad</h6>
                                                            <small class="text-muted">1 day ago • New York, NY</small>
                                                        </div>
                                                    </div>
                                                    <button class="btn btn-sm btn-outline-danger" onclick="revokeSession('tablet')">Revoke</button>
                                                </div>
                                                
                                                <div class="mt-3">
                                                    <button type="button" class="btn btn-outline-danger" onclick="revokeAllSessions()">
                                                        <span class="material-icons-outlined me-2">logout</span>
                                                        Sign Out All Other Sessions
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Avatar & Photo -->
                            <div class="tab-pane fade" id="avatar">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Avatar & Photo</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row g-4">
                                            <div class="col-md-4">
                                                <div class="text-center">
                                                    <img src="https://placehold.co/120x120/3B82F6/FFFFFF?text=JD" alt="Current Avatar" class="rounded-circle mb-3" width="120" height="120" id="currentAvatar">
                                                    <h6>Current Avatar</h6>
                                                </div>
                                            </div>
                                            <div class="col-md-8">
                                                <form id="avatarForm">
                                                    <div class="mb-3">
                                                        <label for="avatarUpload" class="form-label">Upload New Avatar</label>
                                                        <input type="file" class="form-control" id="avatarUpload" accept="image/*">
                                                        <div class="form-text">Upload a square image (JPG, PNG, max 2MB)</div>
                                                    </div>
                                                    
                                                    <div class="mb-3">
                                                        <label class="form-label">Or choose from defaults:</label>
                                                        <div class="d-flex gap-2 flex-wrap">
                                                            <img src="https://placehold.co/50x50/3B82F6/FFFFFF?text=A1" alt="Avatar 1" class="rounded-circle avatar-option" width="50" height="50" data-avatar="avatar-1.jpg">
                                                            <img src="https://placehold.co/50x50/EF4444/FFFFFF?text=A2" alt="Avatar 2" class="rounded-circle avatar-option" width="50" height="50" data-avatar="avatar-2.jpg">
                                                            <img src="https://placehold.co/50x50/10B981/FFFFFF?text=A3" alt="Avatar 3" class="rounded-circle avatar-option" width="50" height="50" data-avatar="avatar-3.jpg">
                                                            <img src="https://placehold.co/50x50/F59E0B/FFFFFF?text=A4" alt="Avatar 4" class="rounded-circle avatar-option" width="50" height="50" data-avatar="avatar-4.jpg">
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="d-flex justify-content-end gap-2">
                                                        <button type="button" class="btn btn-outline-secondary" onclick="removeAvatar()">Remove Avatar</button>
                                                        <button type="submit" class="btn btn-primary">Save Changes</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Subscription -->
                            <div class="tab-pane fade" id="subscription">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Subscription & Billing</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row g-4">
                                            <div class="col-md-6">
                                                <div class="card bg-primary text-white">
                                                    <div class="card-body">
                                                        <h5 class="card-title">Professional Plan</h5>
                                                        <p class="card-text">$79.00 / month</p>
                                                        <p class="card-text"><small>Next billing: January 15, 2025</small></p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="d-flex flex-column gap-2">
                                                    <button type="button" class="btn btn-outline-primary" onclick="changePlan()">
                                                        <span class="material-icons-outlined me-2">upgrade</span>
                                                        Change Plan
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary" onclick="viewBillingHistory()">
                                                        <span class="material-icons-outlined me-2">receipt</span>
                                                        Billing History
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary" onclick="updatePaymentMethod()">
                                                        <span class="material-icons-outlined me-2">credit_card</span>
                                                        Update Payment Method
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <hr>
                                        
                                        <div class="mb-3">
                                            <h6>Usage This Month</h6>
                                            <div class="progress mb-2">
                                                <div class="progress-bar" role="progressbar" style="width: 65%" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                            <small class="text-muted">6,500 / 10,000 API calls used</small>
                                        </div>
                                        
                                        <div class="alert alert-info" role="alert">
                                            <span class="material-icons-outlined me-2">info</span>
                                            Your subscription will automatically renew on January 15, 2025. You can cancel anytime.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Notifications -->
                            <div class="tab-pane fade" id="notifications">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Notification Preferences</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="notificationForm">
                                            <div class="mb-4">
                                                <h6>Email Notifications</h6>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="emailMarketing" checked>
                                                    <label class="form-check-label" for="emailMarketing">
                                                        Marketing emails and product updates
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="emailSecurity" checked>
                                                    <label class="form-check-label" for="emailSecurity">
                                                        Security alerts and account changes
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="emailBilling" checked>
                                                    <label class="form-check-label" for="emailBilling">
                                                        Billing and subscription updates
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="emailWeekly">
                                                    <label class="form-check-label" for="emailWeekly">
                                                        Weekly usage reports
                                                    </label>
                                                </div>
                                            </div>
                                            
                                            <div class="mb-4">
                                                <h6>Push Notifications</h6>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="pushAlerts" checked>
                                                    <label class="form-check-label" for="pushAlerts">
                                                        Important alerts and notifications
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="pushUpdates">
                                                    <label class="form-check-label" for="pushUpdates">
                                                        Product updates and new features
                                                    </label>
                                                </div>
                                            </div>
                                            
                                            <div class="mb-4">
                                                <h6>SMS Notifications</h6>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="smsAlerts">
                                                    <label class="form-check-label" for="smsAlerts">
                                                        Critical security alerts only
                                                    </label>
                                                </div>
                                            </div>
                                            
                                            <div class="d-flex justify-content-end">
                                                <button type="submit" class="btn btn-primary">Save Preferences</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Privacy & Data -->
                            <div class="tab-pane fade" id="privacy">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Privacy & Data</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-4">
                                            <h6>Data Export</h6>
                                            <p class="text-muted">Download a copy of your personal data and account information.</p>
                                            <button type="button" class="btn btn-outline-primary" onclick="exportData()">
                                                <span class="material-icons-outlined me-2">file_download</span>
                                                Request Data Export
                                            </button>
                                        </div>
                                        
                                        <hr>
                                        
                                        <div class="mb-4">
                                            <h6>Privacy Settings</h6>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="profilePublic">
                                                <label class="form-check-label" for="profilePublic">
                                                    Make my profile visible to other users
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="analyticsTracking" checked>
                                                <label class="form-check-label" for="analyticsTracking">
                                                    Allow analytics tracking to improve the service
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="thirdPartySharing">
                                                <label class="form-check-label" for="thirdPartySharing">
                                                    Allow sharing data with trusted partners
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="d-flex justify-content-end">
                                            <button type="button" class="btn btn-primary" onclick="savePrivacySettings()">Save Settings</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Danger Zone -->
                            <div class="tab-pane fade" id="danger">
                                <div class="card border-danger">
                                    <div class="card-header bg-danger text-white">
                                        <h5 class="card-title mb-0">
                                            <span class="material-icons-outlined me-2">warning</span>
                                            Danger Zone
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-4">
                                            <h6 class="text-danger">Deactivate Account</h6>
                                            <p class="text-muted">Temporarily deactivate your account. You can reactivate it anytime by signing in.</p>
                                            <button type="button" class="btn btn-outline-warning" onclick="deactivateAccount()">
                                                <span class="material-icons-outlined me-2">pause_circle</span>
                                                Deactivate Account
                                            </button>
                                        </div>
                                        
                                        <hr>
                                        
                                        <div>
                                            <h6 class="text-danger">Delete Account</h6>
                                            <p class="text-muted">Permanently delete your account and all associated data. This action cannot be undone.</p>
                                            <button type="button" class="btn btn-danger" onclick="deleteAccount()">
                                                <span class="material-icons-outlined me-2">delete_forever</span>
                                                Delete Account Permanently
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/dashboard.js"></script>
    <script src="../assets/js/user-settings.js"></script>
</body>
</html>
