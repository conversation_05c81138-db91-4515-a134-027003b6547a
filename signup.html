<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - SaaSShip</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Google Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    
    <!-- Custom Theme CSS -->
    <link href="assets/css/themes/landing-default.css" rel="stylesheet">
    <link href="assets/css/landing.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html">
                <img src="assets/images/logo.svg" alt="SaaSShip" height="32" class="me-2">
                SaaSShip
            </a>
            
            <div class="navbar-nav ms-auto">
                <span class="nav-text text-muted me-3">Already have an account?</span>
                <a class="btn btn-outline-primary" href="#" data-bs-toggle="modal" data-bs-target="#loginModal">Sign In</a>
            </div>
        </div>
    </nav>

    <!-- Signup Section -->
    <section class="signup-section py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6 col-md-8">
                    <div class="signup-card bg-white rounded-3 shadow p-4 p-lg-5">
                        <div class="text-center mb-4">
                            <h1 class="h3 fw-bold mb-2">Create Your Account</h1>
                            <p class="text-muted">Start your free trial today. No credit card required.</p>
                        </div>

                        <form id="signupForm" novalidate>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="firstName" class="form-label">First Name *</label>
                                    <input type="text" class="form-control" id="firstName" name="firstName" required>
                                    <div class="invalid-feedback">
                                        Please provide your first name.
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="lastName" class="form-label">Last Name *</label>
                                    <input type="text" class="form-control" id="lastName" name="lastName" required>
                                    <div class="invalid-feedback">
                                        Please provide your last name.
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                                <div class="invalid-feedback">
                                    Please provide a valid email address.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="company" class="form-label">Company Name</label>
                                <input type="text" class="form-control" id="company" name="company">
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">Password *</label>
                                <div class="position-relative">
                                    <input type="password" class="form-control" id="password" name="password" required minlength="8">
                                    <button type="button" class="btn btn-link position-absolute end-0 top-50 translate-middle-y pe-3" id="togglePassword">
                                        <span class="material-icons-outlined">visibility</span>
                                    </button>
                                </div>
                                <div class="form-text">Password must be at least 8 characters long.</div>
                                <div class="invalid-feedback">
                                    Password must be at least 8 characters long.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label">Confirm Password *</label>
                                <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required>
                                <div class="invalid-feedback">
                                    Passwords do not match.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="plan" class="form-label">Choose Your Plan</label>
                                <select class="form-select" id="plan" name="plan">
                                    <option value="starter">Starter - $29/month</option>
                                    <option value="professional" selected>Professional - $79/month</option>
                                    <option value="enterprise">Enterprise - $199/month</option>
                                </select>
                            </div>

                            <div class="mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                                    <label class="form-check-label" for="terms">
                                        I agree to the <a href="#" class="text-decoration-none">Terms of Service</a> and <a href="#" class="text-decoration-none">Privacy Policy</a> *
                                    </label>
                                    <div class="invalid-feedback">
                                        You must agree to the terms and conditions.
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="newsletter" name="newsletter">
                                    <label class="form-check-label" for="newsletter">
                                        Subscribe to our newsletter for updates and tips
                                    </label>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary w-100 btn-lg mb-3">
                                Create Account
                            </button>

                            <div class="text-center">
                                <p class="text-muted mb-0">
                                    Already have an account? 
                                    <a href="#" class="text-decoration-none" data-bs-toggle="modal" data-bs-target="#loginModal">Sign in</a>
                                </p>
                            </div>
                        </form>

                        <!-- Social Signup -->
                        <div class="divider my-4">
                            <span class="divider-text text-muted">Or sign up with</span>
                        </div>

                        <div class="row g-2">
                            <div class="col-6">
                                <button type="button" class="btn btn-outline-secondary w-100">
                                    <img src="assets/images/google-icon.svg" alt="Google" width="20" height="20" class="me-2">
                                    Google
                                </button>
                            </div>
                            <div class="col-6">
                                <button type="button" class="btn btn-outline-secondary w-100">
                                    <img src="assets/images/github-icon.svg" alt="GitHub" width="20" height="20" class="me-2">
                                    GitHub
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Features Preview -->
                    <div class="features-preview mt-5">
                        <div class="text-center mb-4">
                            <h3 class="h5 fw-semibold">What you'll get with your free trial:</h3>
                        </div>
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="feature-item text-center">
                                    <div class="feature-icon-sm mb-2">
                                        <span class="material-icons-outlined text-primary">dashboard</span>
                                    </div>
                                    <h6 class="fw-semibold mb-1">Full Dashboard Access</h6>
                                    <p class="text-muted small mb-0">Complete access to all dashboard features</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="feature-item text-center">
                                    <div class="feature-icon-sm mb-2">
                                        <span class="material-icons-outlined text-primary">support_agent</span>
                                    </div>
                                    <h6 class="fw-semibold mb-1">24/7 Support</h6>
                                    <p class="text-muted small mb-0">Get help whenever you need it</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="feature-item text-center">
                                    <div class="feature-icon-sm mb-2">
                                        <span class="material-icons-outlined text-primary">security</span>
                                    </div>
                                    <h6 class="fw-semibold mb-1">Enterprise Security</h6>
                                    <p class="text-muted small mb-0">Bank-level security for your data</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Login Modal -->
    <div class="modal fade" id="loginModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header border-0">
                    <h5 class="modal-title fw-semibold">Welcome Back</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="loginEmail" class="form-label">Email address</label>
                            <input type="email" class="form-control" id="loginEmail" required>
                        </div>
                        <div class="mb-3">
                            <label for="loginPassword" class="form-label">Password</label>
                            <input type="password" class="form-control" id="loginPassword" required>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">Remember me</label>
                        </div>
                        <button type="submit" class="btn btn-primary w-100 mb-3">Sign In</button>
                        <div class="text-center">
                            <a href="#" class="text-decoration-none">Forgot your password?</a>
                        </div>
                    </form>
                </div>
                <div class="modal-footer border-0 justify-content-center">
                    <p class="text-muted mb-0">Don't have an account? <a href="signup.html" class="text-decoration-none">Sign up</a></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
    
    <!-- Signup specific JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Password toggle functionality
            const togglePassword = document.getElementById('togglePassword');
            const passwordInput = document.getElementById('password');
            const toggleIcon = togglePassword.querySelector('.material-icons-outlined');
            
            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                toggleIcon.textContent = type === 'password' ? 'visibility' : 'visibility_off';
            });
            
            // Password confirmation validation
            const confirmPasswordInput = document.getElementById('confirmPassword');
            
            function validatePasswordMatch() {
                if (confirmPasswordInput.value !== passwordInput.value) {
                    confirmPasswordInput.setCustomValidity('Passwords do not match');
                } else {
                    confirmPasswordInput.setCustomValidity('');
                }
            }
            
            passwordInput.addEventListener('input', validatePasswordMatch);
            confirmPasswordInput.addEventListener('input', validatePasswordMatch);
            
            // Form validation
            const signupForm = document.getElementById('signupForm');
            signupForm.addEventListener('submit', function(e) {
                if (!signupForm.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                signupForm.classList.add('was-validated');
            });
        });
    </script>
    
    <style>
        .signup-section {
            min-height: calc(100vh - 70px);
            display: flex;
            align-items: center;
        }
        
        .signup-card {
            max-width: 100%;
        }
        
        .divider {
            position: relative;
            text-align: center;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--border-color);
        }
        
        .divider-text {
            background: white;
            padding: 0 1rem;
            font-size: 0.875rem;
        }
        
        .feature-icon-sm {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
        }
        
        .feature-icon-sm .material-icons-outlined {
            font-size: 24px;
        }
        
        .features-preview {
            opacity: 0.9;
        }
        
        .btn-outline-secondary img {
            filter: grayscale(1);
        }
        
        .btn-outline-secondary:hover img {
            filter: none;
        }
    </style>
</body>
</html>
