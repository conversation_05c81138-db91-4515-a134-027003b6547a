you are a professional web dsigner wih ui/ux experiences ...

can you prepare for me a bootstrap based saas website with vanilla js and responsive design (multi page static)
theme features can be in css variables to I can use the thme for alternative projects. (save every theme in different css files please)
theme options can be different for landing page and dashboard.
please find related images and illustrations for logo and images.


I need following base pages for every projects:

Landing page:

- Home page (hero, features, pricing, faq, cta)
- Blogs and details with thumbnils 
- Signup page 
- Login can be in modal from navbar
- Alert and confirm modals and toast messages necessary
- 404 page
- Sample empty page (you can add )




Dashboard : 

- shrinkable left sidebar, responsive and mobile friendly bootstrap again.
- use google fonts and google outlined icons
- user dashboard example page
- my products list example page (to see table data example, create new item in modal and crud scenario)
- application settings page for admin user (general, apperance, email, sms, payments .. etc)
- user settings page (user info, password, avatar, subcription, 2fa, delete account)

